<template>
	<view class="container">
		<cu-custom bgColor="bg-gradual-blue" :isBack="true">
			<block slot="backText"></block>
			<block slot="content">签到打卡</block>
		</cu-custom>
		<view class="boxTop bg-gradual-blue">
			<view class="topMain bg-white shadow padding-lr-sm padding-tb-xs">
				<view class="flex justify-between ">
					<view class="text-dflg text-bold text-black">我的积分：</view>
					<view @click="loginType == 0 ? goLogin(): goPoints()" class="text-sm text-blue">
						<text>积分记录</text>
						<text class="cuIcon-right"></text>
					</view>
				</view>
				<view class="text-xxxl text-center text-blue text-bold margin-top-xs">{{score}}</view>
				<button @click="todaySignIn? goSignIn() : '' " :class="todaySignIn? 'bg-gradual-orange':'light bg-grey' " class="qdBtn text-lg round shadow">{{todaySignIn? '签到领积分':'今日已签到' }}</button>
			</view>
			
			<view class="clackInBox bg-white shadow padding-tb-sm ">
				<view class="flex justify-between">
					<text class="text-black text-lg text-bold">已签到<text class="text-red text-bold text-xxl margin-lr-xs"> {{signInListData.length}} </text>天</text>
					<text class="text-smdf text-gray">每日更新</text>
				</view>
				<!-- <view class="flex justify-between padding-tb-sm">
					<view class="qdBox_n active">
						<view class="qd_jf text-sm">+1</view>
						<view class="qd_time text-smdf">周一</view>
					</view>
					<view class="qdBox_n active">
						<view class="qd_jf text-sm">+1</view>
						<view class="qd_time text-smdf">周二</view>
					</view>
					<view class="qdBox_n active">
						<view class="qd_jf text-sm">+1</view>
						<view class="qd_time text-smdf">周三</view>
					</view>
					<view class="qdBox_n active">
						<view class="qd_jf text-sm">+2</view>
						<view class="qd_time text-smdf">周四</view>
					</view>
					<view class="qdBox_n" >
						<view class="qd_jf text-sm">+2</view>
						<view class="qd_time text-smdf">周五</view>
					</view>
					<view class="qdBox_n" >
						<view class="qd_jf text-sm">+3</view>
						<view class="qd_time text-smdf">周六</view>
					</view>
					<view class="qdBox_n" >
						<view class="qd_jf text-sm">+3</view>
						<view class="qd_time text-smdf">周日</view>
					</view>
				</view> -->
				
				<view class="text-smdf text-grey margin-top-xs">
					<text class="cuIcon-infofill margin-right-xs"></text>
					<text>签到可增加更多积分，坚持每日打卡哦~</text>
				</view>
				<view class="text-smdf text-grey margin-top-xs">
					<text class="cuIcon-infofill margin-right-xs"></text>
					<text>签到积分可兑换文章智能识别次数！</text>
				</view>
			</view>
		</view>
		
		<view class="centerBox bg-white shadow padding-tb-lg padding-lr-xxl flex justify-between">
			<view>
				<image src="../../static/icon/icon-jf-duihuan.png" mode="widthFix"></image>
				<view class="text-black text-df text-center margin-top-xs">积分兑换</view>
			</view>
			<view>
				<image src="../../static/icon/icon-jf-choujiang.png" mode="widthFix"></image>
				<view class="text-black text-df text-center margin-top-xs">积分抽奖</view>
			</view>
			<view>
				<image src="../../static/icon/icon-jf-hongbao.png" mode="widthFix"></image>
				<view class="text-black text-df text-center margin-top-xs">积分换购</view>
			</view>
		</view>
		
		<view class="bottomBox bg-white shadow">
			<view class="title-header">
				<view class="title-text">
					获 · 取 · 更 · 多 · 积 · 分
				</view>
			</view>
			
			<view class="cu-list menu-avatar">
				<view class="cu-item cur ">
					<image style="background-color: transparent;" class="lg radius cu-avatar" src="../../static/icon/icon-sigin.png" mode="widthFix"></image>
					<view class="content">
						<view>
							<view class="text-cut">
								<text class="text-bold">每日签到</text>
							</view>
						</view>
						<view class="text-gray text-sm flex">
							<view class="text-cut">坚持连续签到可增加更多积分哦~</view>
						</view>
					</view>
					<view class="action">
						<!-- <view class="cu-tag round bg-gradual-blue">签到</view> -->
					</view>
				</view>
				
				<view class="cu-item cur ">
					<image style="background-color: transparent;" class="lg radius cu-avatar" src="../../static/icon/icon-user.png" mode="widthFix"></image>
					<view class="content">
						<view>
							<view class="text-cut">
								<text class="text-bold">邀请好友</text>
							</view>
						</view>
						<view class="text-gray text-sm flex">
							<view class="text-cut">邀请好友可增加更多积分哦~</view>
						</view>
					</view>
					<view class="action">
						<!-- <view class="cu-tag round bg-gradual-blue">邀请</view> -->
					</view>
				</view>
				
				<view class="cu-item cur ">
					<image style="background-color: transparent;" class="lg radius cu-avatar" src="../../static/icon/icon-shop.png" mode="widthFix"></image>
					<view class="content">
						<view>
							<view class="text-cut">
								<text class="text-bold">积分兑好礼</text>
							</view>
						</view>
						<view class="text-gray text-sm flex">
							<view class="text-cut">积分可在积分商城中使用</view>
						</view>
					</view>
					<view class="action">
						<!-- <view class="cu-tag round bg-gradual-blue">使用</view> -->
					</view>
				</view>
				
			</view>
			
		</view>
		
		<view style="height: 40rpx;width: 1rpx;"></view>
		
	</view>
</template>

<script>
	import request from '@/common/request.js';
	import moment from "@/components/moment/index.js"; // 格式化时间 插件
	export default {
		data() {
			return {
				loginType: 0,
				openId: '',
				score:0,
				signInListData: [],
				todaySignIn: false
			}
		},
		onLoad(option) {
			this.getUserInfo();
		},
		methods: {
			getUserInfo(){
				var that = this;
				uni.getStorage({
					key: 'userInfo',
					success: function(res) {
						console.log(res.data);
						that.loginType = 1;
						that.getUserDetail(res.data.openId)
						that.openId = res.data.openId;
						that.signInList(res.data.openId)
						
					},fail:function(err) {
						that.loginType = 0;
					}
				});
			},
			getUserDetail(openId){
				let opts = {
					url: 'api/user/info',
					method: 'GET'
				};
				let params = {
					"openId": openId
				};
				console.log(params)
				request.httpRequest(opts, params).then(res => {
					console.log(res.data.data);
					this.score = res.data.data.score;
					uni.setStorage({
						key: 'userInfo',
						data: res.data.data,
						success: function () {
							console.log('本地Storage 保存成功！');
						}
					});
				});
			},
			signInList(openId){
				let opts = {
					url: 'api/user/signInList',
					method: 'POST'
				};
				let params = {
					"openId": openId
				};
				request.httpRequest(opts, params).then(res => {
					console.log(res.data);
					console.log(todayTime)
					if(res.data.data){
						if( res.data.data.length >=1 ){
							this.signInListData = res.data.data;
							var todayTime = moment().format('YYYY.MM.DD');
							for (var i = 0; i < res.data.data.length; i++) {
							    // 判断当前项是否与目标值相等
							    if (res.data.data[i].time_data === todayTime) {
							        console.log("找到了匹配的项");
									this.todaySignIn = false;
							        break; // 若找到匹配项则结束循环
							    } else {
									this.todaySignIn = true;
							        console.log("未找到匹配的项");
							    }
							}
						}else{
							this.todaySignIn = true;
						}
						
					}
				});
			},
			goSignIn(){
				console.log('签到')
				let opts = {
					url: 'api/user/signIn',
					method: 'POST'
				};
				let params = {
					"openId": this.openId,
					"time_data": moment().format('YYYY.MM.DD')
				};
				request.httpRequest(opts, params).then(res => {
					console.log(res.data.code);
					if(res.data.code == 200){
						this.todaySignIn = false;
						console.log('签到成功')
						this.getUserInfo();
					}
				});
			},
			goLogin(){
				uni.navigateTo({
					url: '/pages/me/profile'
				}) 
			},
			goPoints(){
			 uni.navigateTo({
			 	url: '/pages/me/points'
			 }) 
			},
		}
	}
</script>

<style lang="scss" scoped>
	.boxTop{
		width: 750rpx;
		height: 380rpx;
		padding-top: 30rpx;
		position: relative;
		margin-bottom: 160rpx;
		.topMain{
			width: 650rpx;
			margin: 0rpx auto;
			height: 200rpx;
			border-radius: 18rpx;
			position: relative;
			.qdBtn{
				position: absolute;
				bottom: -30rpx;
				width: 500rpx;
				height: 80rpx;
				line-height: 80rpx;
				left: 75rpx;
			}
		}
		.clackInBox{
			width: 700rpx;
			// height: 300rpx;
			position: absolute;
			left: 25rpx;
			bottom: -120rpx;
			border-radius: 18rpx;
			padding-left: 25rpx;
			padding-right: 25rpx;
			.qdBox_n.active{
				background: linear-gradient(to top, #FF391C, #FF5F03);
				box-shadow: 6rpx 6rpx 8rpx rgba(26, 26, 26, 0.2);
				color: #ffffff;
				.qd_jf{
					background-color: #FCED4F;
					color: #FF391C;
				}
			}
			.qdBox_n{
				width: 85rpx;
				padding: 10rpx 0;
				background-color: #f5f5f5;
				display: flex;
				flex-direction: column;
				align-items: center;
				border-radius: 18rpx;
				
				.qd_jf{
					box-shadow: 6rpx 6rpx 8rpx rgba(26, 26, 26, 0.2);
					font-size: 22rpx;
					letter-spacing: 2rpx;
					display: inline-block;
					width: 55rpx;
					height: 55rpx;
					line-height: 55rpx;
					background: #e6e6e6;
					border-radius: 50%;
					text-align: center;
					margin-top: 4rpx;
					margin-bottom: 10rpx;
				}
			}
		}
	}
	
	.centerBox{
		width: 700rpx;
		border-radius: 18rpx;
		margin: 0 auto;
		image{
			width: 120rpx;
			border-radius: 14rpx;
		}
	}
	
	.bottomBox{
		width: 700rpx;
		border-radius: 18rpx;
		margin: 30rpx auto 0rpx;
		.title-header {
			display: flex;
			height: 120rpx;
			font-size: 38rpx;
			align-items: center;
			justify-content: center;
			/* padding: 40rpx 0 0 0; */
			font-weight: bold;
			background-image: url(../../static/icon/wccswF.png);
			background-size: cover;
		}
		.title-text {
			background-image: -webkit-linear-gradient(0deg, #ff8a34, #FBBD12);
			-webkit-background-clip: text;
			-webkit-text-fill-color: transparent;
			/* border:10px solid #ddd;
		  border-image: -webkit-linear-gradient(red,yellow);
		 	border-image: -moz-linear-gradient(red,yellow);
		  border-image: linear-gradient(red,yellow);  */
		}
	}
	
	
</style>
