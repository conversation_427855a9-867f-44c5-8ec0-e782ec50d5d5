/**
 * 简化版HTTP请求客户端 - 快速好用
 * 集成认证、错误处理等核心功能
 */

const baseUrl = "https://www.gaibiyou.com/cww-api/";

class HttpClient {
  constructor() {
    this.baseURL = baseUrl;
  }

  /**
   * 检查登录状态
   */
  isLoggedIn() {
    const token = uni.getStorageSync("token");
    const userInfo = uni.getStorageSync("userInfo");
    return !!(token && userInfo);
  }

  /**
   * 跳转登录页
   */
  redirectToLogin() {
    uni.clearStorageSync();
    uni.showToast({
      title: "请重新登录",
      icon: "none",
      complete: () => {
        setTimeout(() => {
          uni.reLaunch({ url: "/pages/user/login" });
        }, 1500);
      },
    });
  }

  /**
   * 处理请求错误
   */
  handleError(error, showToast = true) {
    console.error("Request Error:", error);

    if (!showToast) return;

    if (error.code === 401) {
      this.redirectToLogin();
    } else {
      uni.showToast({
        title: error.message || "请求失败",
        icon: "none",
      });
    }
  }

  /**
   * 核心请求方法 - 简化版
   */
  async request(url, options = {}) {
    const {
      method = "GET",
      data = {},
      needAuth = true,
      showLoading = true,
      loadingText = "加载中...",
      showError = true,
    } = options;

    // 显示loading
    if (showLoading) {
      uni.showLoading({ title: loadingText, mask: true });
    }

    // 构建请求头
    const headers = {
      "Content-Type": "application/json",
      "X-Requested-With": "XMLHttpRequest",
    };

    // 添加token
    if (needAuth) {
      const token = uni.getStorageSync("token");
      if (!token) {
        if (showLoading) uni.hideLoading();
        this.redirectToLogin();
        throw new Error("未登录");
      }
      headers.Authorization = `Bearer ${token}`;
    }

    try {
      const response = await new Promise((resolve, reject) => {
        uni.request({
          url: this.baseURL + url,
          method,
          data,
          header: headers,
          dataType: "json",
          timeout: 10000,
          success: resolve,
          fail: reject,
        });
      });

      if (showLoading) uni.hideLoading();

      const result = response.data;

      // 处理业务状态码
      if (result.code === 200) {
        return result;
      } else if (result.code === 401) {
        this.redirectToLogin();
        throw { code: 401, message: "登录已过期" };
      } else {
        const error = {
          code: result.code,
          message: result.message || "请求失败",
        };
        if (showError) this.handleError(error);
        throw error;
      }
    } catch (error) {
      if (showLoading) uni.hideLoading();

      if (!error.code) {
        error = { code: "NETWORK_ERROR", message: "网络连接失败" };
      }

      if (showError) this.handleError(error);
      throw error;
    }
  }

  /**
   * GET请求
   */
  get(url, params = {}, options = {}) {
    const queryString = Object.keys(params)
      .map(
        (key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`
      )
      .join("&");

    const finalUrl = queryString ? `${url}?${queryString}` : url;

    return this.request(finalUrl, { method: "GET", ...options });
  }

  /**
   * POST请求
   */
  post(url, data = {}, options = {}) {
    return this.request(url, { method: "POST", data, ...options });
  }

  /**
   * PUT请求
   */
  put(url, data = {}, options = {}) {
    return this.request(url, { method: "PUT", data, ...options });
  }

  /**
   * DELETE请求
   */
  delete(url, options = {}) {
    return this.request(url, { method: "DELETE", ...options });
  }
}

// 创建默认实例
const httpClient = new HttpClient();

export default httpClient;
export { HttpClient };
