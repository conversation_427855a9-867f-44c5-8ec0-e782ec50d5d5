<template>
  <view class="components-home" style="position: relative">
    <web-view
      :src="url"
      @message="handleH5Message"
      id="h5WebView"
      :webview-styles="webviewStyles"
    ></web-view>
  </view>
</template>

<script>
import { webUrl } from "@/common/request.js";
import request from "@/common/request.js";
export default {
  data() {
    return {
      webUrl,
      id: "",
      webviewStyles: {
        top: "44px", // 预留顶部导航栏高度
        height: "calc(100% - 44px)",
      },
    };
  },
  onLoad(option) {
    this.id = option.id;
    // setTimeout(() => {
    //   this.initWebViewCommunication();
    // }, 10);

    // const pages = getCurrentPages();

    // const webviewPage = pages[pages.length - 2];

    // webviewPage.setData(
    //   {
    //     shouldReattachWebview: true,
    //   },
    //   () => {
    //     app.wechat.navigateBack();
    //   }
    // );
  },
  computed: {
    token() {
      return uni.getStorageSync("token");
    },
    url() {
      return `${webUrl}/evaluation-h5-detail/${this.id}?token=${this.token}`;
    },
  },
  methods: {
    initWebViewCommunication() {
      this.setupMiniProgramBridge();
    },
    setupMiniProgramBridge() {
      uni.$on("h5Message", this.handleH5Message);
      // 向H5注入全局方法
      const script = `
    window.sendToMiniProgram = (data) => {
      uni.postMessage({ data });
    };
  `;
      // 动态执行脚本（需在web-view加载完成后调用）
    },

    // 处理H5发来的消息
    handleH5Message(e) {
      console.log(e, "H5 Action:");

      const messageData = e.detail.data[0];
      const { action, data } = messageData;
      switch (action) {
        case "modifyComposition":
          // 修改本篇作文
          uni.navigateTo({
            url: "/pages/job/recognition-score?id=" + data.evaluationId,
          });
          break;

        case "generateReport":
          this.handleGenerateReport(data);
          break;

        default:
          console.warn("未知的H5 action:", action);
          break;
      }
    },

    // 处理修改作文请求
    handleModifyComposition(data) {
      console.log("处理修改作文请求:", data);

      if (data && data.evaluationId) {
        uni.navigateTo({
          url: "/pages/job/upload-evaluation?id=" + data.evaluationId,
        });
      } else {
        console.warn("修改作文请求缺少必要参数:", data);
        uni.showToast({
          title: "参数错误",
          icon: "none",
        });
      }
    },

    // 处理生成报告请求
    handleGenerateReport(data) {
      console.log("处理生成报告请求:", data);

      // 参数验证
      if (!data || !data.evaluationId) {
        console.warn("生成报告请求缺少必要参数:", data);
        uni.showToast({
          title: "参数错误，无法生成报告",
          icon: "none",
        });
        return;
      }

      uni.showLoading({
        title: "正在生成报告...",
      });

      // 设置请求参数，指定响应类型为arraybuffer以接收文档流
      let opts = {
        url: "biz_composition_log/export_word/" + data.evaluationId,
        method: "GET",
        responseType: "arraybuffer", // 接收二进制数据
      };

      // 使用uni.request直接处理文档流
      uni.request({
        url: request.baseUrl + opts.url,
        method: opts.method,
        responseType: opts.responseType,
        header: {
          Authorization: "Bearer " + uni.getStorageSync("token"),
        },
        success: (res) => {
          uni.hideLoading();

          console.log("API响应完整数据:", res);
          console.log("响应状态码:", res.statusCode);
          console.log("响应数据类型:", typeof res.data);
          console.log("数据长度:", res.data ? res.data.byteLength : 0);

          // 检查响应状态
          if (res.statusCode !== 200) {
            console.error("API响应错误，状态码:", res.statusCode);
            uni.showToast({
              title: "报告生成失败，服务器错误",
              icon: "none",
            });
            return;
          }

          // 检查返回的文档流数据
          if (!res.data || res.data.byteLength === 0) {
            console.warn("API返回空的文档流数据");
            uni.showToast({
              title: "报告生成失败，数据为空",
              icon: "none",
            });
            return;
          }

          console.log("检测到文档流数据，开始保存到本地文件");
          // 处理文档流数据
          this.saveDocumentStreamToFile(res.data, data.evaluationId);
        },
        fail: (err) => {
          uni.hideLoading();
          console.error("API请求失败:", err);

          let errorMsg = "网络请求失败";
          if (err.errMsg) {
            if (err.errMsg.includes("timeout")) {
              errorMsg = "请求超时，请重试";
            } else if (err.errMsg.includes("network")) {
              errorMsg = "网络连接失败";
            }
          }

          uni.showModal({
            title: "报告生成失败",
            content: errorMsg + "，是否重试？",
            showCancel: true,
            confirmText: "重试",
            success: (modalRes) => {
              if (modalRes.confirm) {
                this.handleGenerateReport(data);
              }
            },
          });
        },
      });
    },

    // 保存文档流到本地文件
    saveDocumentStreamToFile(arrayBuffer, evaluationId) {
      console.log("开始保存文档流到本地文件");
      console.log("文档流大小:", arrayBuffer.byteLength, "字节");

      // 生成临时文件路径
      const fileName = `report_${evaluationId}_${Date.now()}.docx`;
      const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;

      console.log("目标文件路径:", filePath);

      // 使用微信小程序文件系统API保存文件
      const fileManager = wx.getFileSystemManager();

      fileManager.writeFile({
        filePath: filePath,
        data: arrayBuffer,
        encoding: "binary",
        success: () => {
          console.log("文档流保存成功，文件路径:", filePath);

          // 保存成功后打开文档
          this.openSavedDocument(filePath);
        },
        fail: (err) => {
          console.error("保存文档流失败:", err);

          let errorMsg = "保存文档失败";
          if (err.errMsg) {
            if (err.errMsg.includes("no such file or directory")) {
              errorMsg = "目录不存在，无法保存文档";
            } else if (err.errMsg.includes("not enough storage")) {
              errorMsg = "存储空间不足，请清理手机存储";
            } else if (err.errMsg.includes("permission")) {
              errorMsg = "没有文件写入权限";
            }
          }

          uni.showModal({
            title: "保存失败",
            content: errorMsg,
            showCancel: false,
          });
        },
      });
    },

    // 打开已保存的文档
    openSavedDocument(filePath) {
      console.log("尝试打开已保存的文档:", filePath);

      uni.openDocument({
        filePath: filePath,
        fileType: "docx",
        showMenu: true,
        success: () => {
          console.log("成功打开报告文档");
          uni.showToast({
            title: "报告已打开",
            icon: "success",
          });
        },
        fail: (err) => {
          console.error("打开报告文档失败:", err);

          let errorMsg = "无法打开文档";
          if (err.errMsg) {
            if (err.errMsg.includes("file not exist")) {
              errorMsg = "文件不存在或已损坏";
            } else if (err.errMsg.includes("permission")) {
              errorMsg = "没有文件访问权限";
            } else if (err.errMsg.includes("format")) {
              errorMsg = "文档格式损坏或不支持";
            } else if (err.errMsg.includes("system error")) {
              errorMsg = "系统错误，请重试";
            }
          }

          uni.showModal({
            title: "打开失败",
            content: `${errorMsg}，是否重新生成报告？`,
            showCancel: true,
            confirmText: "重新生成",
            success: (res) => {
              if (res.confirm) {
                // 重新生成报告
                this.handleGenerateReport({
                  evaluationId: filePath.match(/report_(\d+)_/)?.[1],
                });
              }
            },
          });
        },
      });
    },

    // 直接打开文档（用于文件路径格式）
    openDocumentDirectly(filePath) {
      console.log("尝试直接打开文档:", filePath);

      // 检测文件类型
      let fileType = "docx";
      if (filePath.toLowerCase().includes(".pdf")) {
        fileType = "pdf";
      } else if (filePath.toLowerCase().includes(".doc")) {
        fileType = "doc";
      }

      console.log("检测到文件类型:", fileType);

      uni.openDocument({
        filePath: filePath,
        fileType: fileType,
        showMenu: true,
        success: () => {
          console.log("成功打开报告文档");
          uni.showToast({
            title: "报告已打开",
            icon: "success",
          });
        },
        fail: (err) => {
          console.error("打开报告文档失败:", err);

          // 根据错误类型提供不同的提示
          let errorMsg = "无法打开文档";
          if (err.errMsg && err.errMsg.includes("file not exist")) {
            errorMsg = "文件不存在，请重新生成";
          } else if (err.errMsg && err.errMsg.includes("permission")) {
            errorMsg = "没有文件访问权限";
          } else if (err.errMsg && err.errMsg.includes("format")) {
            errorMsg = "文档格式不支持";
          }

          uni.showModal({
            title: "打开失败",
            content: `${errorMsg}，是否重新生成报告？`,
            success: (res) => {
              if (res.confirm) {
                // 可以在这里添加重试逻辑
                console.log("用户选择重新生成");
              }
            },
          });
        },
      });
    },

    // 下载并打开文档
    downloadAndOpenDocument(url) {
      console.log("开始下载文档:", url);

      uni.showLoading({
        title: "正在下载文档...",
      });

      uni.downloadFile({
        url: url,
        success: (res) => {
          uni.hideLoading();
          console.log("下载完成，状态码:", res.statusCode);
          console.log("临时文件路径:", res.tempFilePath);

          if (res.statusCode === 200) {
            // 检测文件类型
            let fileType = "docx";
            if (url.toLowerCase().includes(".pdf")) {
              fileType = "pdf";
            } else if (url.toLowerCase().includes(".doc")) {
              fileType = "doc";
            }

            console.log("准备打开文档，类型:", fileType);

            uni.openDocument({
              filePath: res.tempFilePath,
              fileType: fileType,
              showMenu: true,
              success: () => {
                console.log("成功打开下载的报告文档");
                uni.showToast({
                  title: "报告已打开",
                  icon: "success",
                });
              },
              fail: (err) => {
                console.error("打开下载文档失败:", err);

                // 提供更详细的错误信息
                let errorMsg = "无法打开下载的文档";
                if (err.errMsg && err.errMsg.includes("format")) {
                  errorMsg = "文档格式不支持，请联系客服";
                }

                uni.showModal({
                  title: "打开失败",
                  content: errorMsg,
                  showCancel: false,
                });
              },
            });
          } else {
            console.error("下载失败，状态码:", res.statusCode);
            uni.showModal({
              title: "下载失败",
              content: `服务器响应错误（状态码：${res.statusCode}），请稍后重试`,
              showCancel: false,
            });
          }
        },
        fail: (err) => {
          uni.hideLoading();
          console.error("下载文件失败:", err);

          // 根据错误类型提供不同提示
          let errorMsg = "网络错误，下载失败";
          if (err.errMsg && err.errMsg.includes("timeout")) {
            errorMsg = "下载超时，请检查网络连接";
          } else if (err.errMsg && err.errMsg.includes("network")) {
            errorMsg = "网络连接失败，请重试";
          }

          uni.showModal({
            title: "下载失败",
            content: errorMsg,
            confirmText: "重试",
            success: (res) => {
              if (res.confirm) {
                // 重试下载
                this.downloadAndOpenDocument(url);
              }
            },
          });
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
