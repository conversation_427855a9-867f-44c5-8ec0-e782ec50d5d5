<!-- 首页 -->
<template>
  <view class="components-home" style="position: relative">
    <view
      class="topBg"
      style="background-image: url(https://cdn.zhoukaiwen.com/cewen_me_bg.png)"
    >
      <view class="cu-custom">
        <view
          class="fixed"
          :style="style"
          style="justify-content: center; align-content: center"
        >
          <view
            class="text-white text-bold text-lg text-center"
            style="position: relative"
            :style="[{ top: StatusBar + 'px' }]"
          >
            <text
              @click="goBack"
              class="cuIcon-back text-left"
              style="position: absolute; left: 0; top: 6rpx"
            ></text>
            <text class="">历史测评</text>
          </view>
        </view>
      </view>
    </view>

    <view class="mainBox" :style="[{ top: CustomBar + 10 + 'px' }]">
      <view class="margin-top-sm text-lgxl text-white margin-bottom"
        >《{{ title }}》</view
      >

      <!-- 列表区域 -->
      <view class="listBox bg-white shadow">
        <view class="text-green3">
          <text class="cuIcon-titles"></text>
          <text class="text-black text-bold"
            >共修改{{ historyList.length - 1 }}次</text
          >
        </view>

        <view
          v-for="(item, index) in historyList"
          :key="index"
          @click="toDetails(item)"
        >
          <view class="text-right text-gray">{{
            $u.timeFormat(item.submitTime, "yyyy-mm-dd hh:MM:ss")
          }}</view>
          <view class="flex justify-start align-center margin-top">
            <view>
              <view class="numBox">{{ index + 1 }}</view>
            </view>
            <view
              class="margin-left-sm flex justify-between text-center"
              style="width: 100%"
            >
              <view style="width: 33.33%">
                <view class="text-black text-xl text-bold">{{
                  item.score
                }}</view>
                <view class="margin-top-xs text-gray">投稿得分</view>
              </view>
              <view style="width: 33.33%">
                <view class="text-black text-xl text-bold">{{
                  item.percentage
                }}</view>
                <view class="margin-top-xs text-gray">满意度</view>
              </view>
              <view style="width: 33.33%">
                <view class="text-black text-xl text-bold">{{
                  getScoreLevel(item.percentage).levelLabel
                }}</view>
                <view class="margin-top-xs text-gray">评价</view>
              </view>
            </view>
          </view>
          <view
            class="flex justify-between align-center padding-right-sm margin-tb padding-bottom-sm"
            style="padding-left: 85rpx; border-bottom: 1rpx solid #dcdcdc"
          >
            <view>
              <text class="margin-right-sm">投稿星级</text>
              <text
                v-for="i in 3"
                :key="i"
                class="cuIcon-favorfill margin-right-xs"
                :style="{ color: i < item.star ? '#f0142f' : '#b7b9b3' }"
              ></text>
            </view>
            <!-- <view
              class="text-smdf"
              style="
                background-color: #f0142f14;
                color: #f0142f;
                padding: 10rpx 26rpx;
                border-radius: 10rpx;
              "
            >
              <text>删除记录</text>
            </view> -->
          </view>
        </view>
      </view>

      <!-- 列表区域 -->
      <view class="listBox bg-white shadow">
        <view class="text-green3">
          <text class="cuIcon-titles"></text>
          <text class="text-black text-bold">修改分数统计</text>
        </view>
        <view class="margin-top">
          <view class="chartsMain">
            <canvas
              canvas-id="canvasArea"
              id="canvasArea"
              class="charts"
            ></canvas>
          </view>
        </view>
      </view>
    </view>

    <view class="page-box" v-if="false">
      <view>
        <view class="centre">
          <image
            src="https://cdn.zhoukaiwen.com/noData1.png"
            mode="widthFix"
          ></image>
          <view class="explain">
            暂无数据
            <view class="tips">可以去看看其他板块</view>
          </view>
        </view>
      </view>
    </view>

    <view style="height: 140rpx; width: 1rpx"></view>
  </view>
</template>

<script>
import request from "@/common/request.js";
import { getScoreLevel } from "@/common/constants.js";
// 图表
import uCharts from "@/components/u-charts/u-charts.js";
var _self;
var canvaArea = null;
export default {
  data() {
    return {
      CustomBar: this.CustomBar,
      maxHeight: 0,
      historyList: [],
      title: "",
      getScoreLevel,

      // 折线图
      cWidth: "",
      cHeight: "",
      pixelRatio: 1,
      gaugeWidth: 15,
      chartData: {
        categories: [
          {
            value: 0.2,
            color: "#2fc25b",
          },
          {
            value: 0.8,
            color: "#f04864",
          },
          {
            value: 1,
            color: "#1890ff",
          },
        ],
        series: [
          {
            name: "完成率",
            data: 0.85,
          },
        ],
      },
      Area: {
        categories: [],
        series: [
          {
            name: "得分统计",
            data: [],
            color: "#07a069",
          },
        ],
      },
    };
  },
  computed: {
    style() {
      const StatusBar = this.StatusBar;
      const CustomBar = this.CustomBar;
      const bgImage = this.bgImage;
      const style = `height:${CustomBar}px;padding-top:${StatusBar}px;`;
      if (this.bgImage) {
        style = `${style}background-image:url(${bgImage});`;
      }
      return style;
    },
  },
  onLoad(options) {
    this.title = decodeURIComponent(options.title);
    this.getList(options.id);

    // charts
    _self = this;
    this.cWidth = uni.upx2px(650);
    this.cHeight = uni.upx2px(420);
  },
  methods: {
    getServerData() {
      _self.showArea();
    },
    // 折线图
    showArea() {
      const ctx = uni.createCanvasContext("canvasArea", this.chartData);
      canvaArea = new uCharts({
        $this: _self,
        context: ctx,
        type: "area",
        padding: [15, 0, 0, 0], // 图表的边距
        fontSize: 11,
        dataLabel: true, // 是否显示数据标签
        legend: true,
        dataPointShape: true,
        background: "#FFFFFF",
        pixelRatio: _self.pixelRatio,
        categories: _self.Area.categories,
        series: _self.Area.series,
        animation: true,
        xAxis: {
          type: "grid",
          gridColor: "#CCCCCC",
          gridType: "dash",
          dashLength: 8,
        },
        yAxis: {
          gridType: "dash",
          gridColor: "#CCCCCC",
          dashLength: 8,
          splitNumber: 5,
          min: 10,
          max: 180,
        },
        width: _self.cWidth * _self.pixelRatio,
        height: _self.cHeight * _self.pixelRatio,
        extra: {
          area: {
            type: "straight",
            opacity: 0.2,
            addLine: true,
            width: 2,
          },
        },
      });
    },
    getList(id) {
      const data = {
        initLogId: id,
      };
      let opts = {
        url: `biz_composition_log/user_list`,
        method: "POST",
      };
      request.httpTokenRequest(opts, data).then((res) => {
        const data = res.data || [];
        this.historyList = data;
        // Object.values(
        //   data.reduce((acc, item) => {
        //     const key =
        //       item.compositionId != 0 && item.compositionId != null
        //         ? item.compositionId
        //         : item.title;

        //     console.log(key);
        //     if (!acc[key]) {
        //       acc[key] = {
        //         open: false,
        //         ...item,
        //         submitTime: item.createTime
        //           ? this.$u.timeFormat(item.createTime, "yyyy年mm月dd日 hh:MM")
        //           : "",
        //         compositionBeginTime: item.compositionBeginTime
        //           ? this.$u.timeFormat(
        //               item.compositionBeginTime,
        //               "yyyy年mm月dd日 hh:MM"
        //             )
        //           : "",
        //         compositionEndTime: item.compositionEndTime
        //           ? this.$u.timeFormat(
        //               item.compositionEndTime,
        //               "yyyy年mm月dd日 hh:MM"
        //             )
        //           : "",
        //         detail: [],
        //       };
        //     }
        //     acc[key].detail.push({
        //       ...item,
        //       submitTime: item.createTime
        //         ? this.$u.timeFormat(item.createTime, "yyyy年mm月dd日 hh:MM")
        //         : "",
        //     });
        //     return acc;
        //   }, {})
        // );
        console.log(this.historyList);
        console.log(this.Area);
        this.historyList.forEach((item, index) => {
          // 对每个item执行操作，例如打印索引和值
          this.Area.categories.push(
            this.$u.timeFormat(
              this.historyList[index].submitTime,
              "mm-dd hh:MM"
            )
          );
          this.Area.series[0].data.push(this.historyList[index].score);
        });
        this.getServerData();
      });
    },
    toDetails(item) {
      uni.navigateTo({
        url: `/pages/me/historyDetail?id=${item.id}`,
      });
    },
    goBack() {
      uni.navigateBack({
        delta: 1,
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.topBg {
  background-size: 100% 100%;
  /* background-size: cover; */
  height: 760rpx;
  width: 750rpx;
  overflow: hidden;
  flex-direction: column;
  padding: 20rpx;
  position: absolute;
  top: 0;
  left: 0;
}

.mainBox {
  width: 700rpx;
  position: absolute;
  left: 25rpx;
}
.chartsMain {
  width: 650rpx;
  height: 380rpx;
  padding-top: 15rpx;
  background: #fff;
  margin-bottom: 24rpx;
  border-top: 2rpx solid #f2f2f2;
  .charts {
    width: 650rpx;
    height: 380rpx;
    box-sizing: border-box;
  }
}

.inputBox {
  position: relative;

  .topInput {
    height: 78rpx;
    border-radius: 20rpx;
    padding: 0 20rpx;
    line-height: 78rpx;
    box-sizing: border-box;
  }

  .inputIcon {
    width: 42rpx;
    position: absolute;
    right: 30rpx;
    top: 16rpx;
    z-index: 999;
  }
}

.listBox {
  padding: 25rpx 30rpx;
  border-radius: 15rpx;
  margin: 20rpx 0;
}

.numBox {
  width: 46rpx;
  height: 46rpx;
  border-radius: 50%;
  text-align: center;
  line-height: 46rpx;
  border: 2rpx solid #07a069;
  color: #07a069;
  font-size: 28rpx;
}

.item {
  display: flex;
  flex-direction: column; //决定了下拉框在下面
  background-color: #fff;
  padding: 0 30rpx;

  .title {
    display: flex;
    flex-direction: row; //左部分是信息，右部分是箭头
    justify-content: space-between;

    .info-box {
      display: flex;
      flex-direction: column; //上部分是作文标题和文章类型，下部分是作文每次数据
      justify-content: flex-start;

      .up-box {
        display: flex;
        flex-direction: row;
        margin-top: 10rpx; //如果这里改了handleClick算高度那里也要改
        align-items: flex-end;

        .flag-box {
          margin-left: 15rpx;
          width: 58rpx;
          text-align: center;
          border-radius: 6rpx;
        }

        .run-box {
          font-size: 26rpx;
          margin-left: 25rpx;
        }

        .divider {
          margin-left: 25rpx;
          margin-bottom: 5rpx;
          background: #b7b9b3;
          width: 1rpx;
          height: 70%;
          opacity: 0.5;
        }
      }

      .down-box {
        display: flex;
        flex-direction: row;
        font-weight: 500;
        height: 40rpx;
        font-size: 24rpx;
        color: #acb1b5;
        margin-top: 12rpx;
      }
    }

    .up {
      margin-top: 40rpx;
      width: 20rpx;
      height: 20rpx;
      opacity: 0.8;
      transition: all ease 0.6s;
      transform: rotate(0deg);
    }

    .down {
      margin-top: 40rpx;
      width: 20rpx;
      height: 20rpx;
      opacity: 0.8;
      transition: all ease 0.6s;
      transform: rotate(-180deg);
    }
  }

  .sub-box {
    display: flex;
    flex-direction: column;
    background: #f6f6f6;
    border-radius: 20rpx;
    height: 120rpx;
    margin-top: 20rpx;

    .up-box {
      display: flex;
      flex-direction: row;
      align-items: flex-end;
      font-family: Roboto;
      font-weight: bold;

      .run-box {
        margin-left: 25rpx;
      }

      .divider {
        margin-left: 25rpx;
        margin-bottom: 5rpx;
        background: #b7b9b3;
        width: 1rpx;
        height: 100%;
        opacity: 0.5;
      }
    }

    .down-box {
      display: flex;
      flex-direction: row;
      height: 18px;
    }
  }
}

.p_item {
  overflow: hidden;
  transition: max-height 0.4s ease;
}
</style>
