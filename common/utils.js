export function fileFun(url) {
  const num = url.lastIndexOf("_") + 1;
  return url.substring(num);
}

export function parseResult(data) {
  const inputObj = data
    .replace(/\\/g, "\\\\")
    .replace(/\n/g, "\\n")
    .replace(/\r/g, "\\r")
    .replace(/\t/g, "\\t")
    .replace(/("")+/g, '""')
    .replace(/'/g, "&#39;")
    .replace(/ /g, "&nbsp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;");
  return JSON.parse(inputObj) || "无法识别文本内容";
}

export const packageType = {
  evaluation: "识别与测评",
  microLessons: "作文微课",
};

/**
 * 防抖函数（支持立即执行和非立即执行两种模式）
 * @param {Function} func 需要执行的函数
 * @param {number} [delay=2000] 延迟时间/冷却时间，单位毫秒，默认为 2000ms
 * @param {boolean} [immediate=true] 是否立即执行。true: 第一次触发时立即执行，然后进入冷却；false: 触发后延迟执行（标准防抖）
 * @returns {Function} 返回一个经过防抖处理的新函数
 */
export function debounce(func, delay = 2000, immediate = true) {
  let timer = null;

  return function (...args) {
    const context = this;

    if (timer) {
      clearTimeout(timer);
    }

    if (immediate) {
      const callNow = !timer;
      timer = setTimeout(() => {
        timer = null;
      }, delay);
      if (callNow) {
        // 在Vue的方法中使用时，func的上下文this会自动指向Vue实例，所以这里是安全的
        func.apply(context, args);
      }
    } else {
      timer = setTimeout(() => {
        func.apply(context, args);
      }, delay);
    }
  };
}
