<!-- 关于我们 -->
<template>
  <view class="container">
    <cu-custom bgColor="bg-green3" :isBack="true">
      <block slot="backText"></block>
      <block slot="content">关于我们</block>
    </cu-custom>

    <view class="mainBox">
      <image class="logo" src="../../static/logo.jpg" mode="widthFix"></image>
      <view class="text-center text-bold text-black text-xl margin-bottom"
        >作文智判慧改</view
      >
      <view class="text-center padding-lr" style="line-height: 1.6"
        >作文智判慧改竞赛平台是一款集作文竞赛发布、报名、参赛、自动批改和反馈的竞赛运营平台，竞赛组织者可以按照年龄、年级、性别或文体要求发布作文竞赛要求；参赛者通过微信小程序报名参赛，并通过拍照或上传图片实现系统自动评分；系统可以依据自动评分结果或竞赛组织者的打分结果实现排名，并发布竞赛结果。</view
      >
      <view
        class="text-center padding-lr margin-top-lg"
        style="line-height: 1.6"
        >本产品为了满足本公司运营作文类竞赛及相关增值业务需要而开发，通过嵌入中科院手写汉字识别技术和测文网连续文本自动评分技术，实现竞赛类作文的自动批阅，帮助参赛者在参赛过程中提升作文的书写水平，从而达到提升语文考试成绩的目标。</view
      >
    </view>
  </view>
</template>

<script>
import request from "@/common/request.js";
export default {
  data() {
    return {};
  },
  onLoad(option) {},
  methods: {
    onShareAppMessage(res) {
      return {
        title: "轻松一课，来看看吧～",
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.mainBox {
  width: 750rpx;
}
.logo {
  width: 140rpx;
  margin: 90rpx auto 30rpx;
  display: block;
  border-radius: 12rpx;
}
</style>
