<!-- 首页 -->
<template>
	<view class="components-home" style="position: relative">
		<view class="topBg" style="background-image: url(https://cdn.zhoukaiwen.com/cewen_me_bg.png)">
			<view class="cu-custom">
				<view class="fixed text-center" :style="style" style="justify-content: center">
					<view class="text-white text-bold text-lg" :style="[{ top: StatusBar + 'px' }]">
						历史测评
					</view>
				</view>
			</view>
		</view>

		<view class="mainBox" :style="[{ top: CustomBar + 10 + 'px' }]">
			<!-- 搜索框 -->
			<view class="inputBox">
				<input v-model="searchInput" class="topInput bg-white" placeholder="输入文章名称搜索" />
				<image @click="getList" class="inputIcon" src="/static/icon_history_search.svg" mode="widthFix"></image>
			</view>
			<view class="margin-tb-sm text-white" style="opacity: 0.75">{{ userInfo.realName }} 共测评{{ historyTotal }}篇
			</view>

			<!-- 列表区域 -->
			<view class="listBox">
				<view class="listItem bg-white shadow" v-for="(item, index) in historyList" :key="index">
					<view class="flex justify-start align-center">
						<view class="cu-tag radius sm text-white" :style="{
                backgroundColor:
                  CompositionEvaluateSourcesColor[item.evaluateSources],
              }">
							{{
                CompositionEvaluateSourcesMap[item.evaluateSources] ||
                "自主测评"
              }}
						</view>
						<view class="margin-left-xs text-black text-bold">《{{ item.title }}》</view>
					</view>
					<view class="margin-top-sm">
						<text>最终分数：</text>
						<text class="text-red">{{ item.lastScore }}分</text>
					</view>
					<view class="margin-top-sm flex align-center">
						<text class="text-gray">{{ item.author }}</text>
						<text class="margin-lr-sm text-green3" style="opacity: 0.35">|</text>
						<text class="text-gray">{{ item.grade }}</text>
						<text class="margin-lr-sm text-green3" style="opacity: 0.35">|</text>
						<text class="text-gray">{{ item.wenTi }}</text>
					</view>

					<view class="u-border-top margin-top padding-top padding-bottom-xs flex justify-between">
						<view class="text-gray">{{ item.submitTime | timeF }}</view>
						<view @click="goHistoryList(item)" class="text-green3">
							<text>共修改{{ item.lastEvaluationCount }}次</text>
							<text class="cuIcon-right" style="margin-left: 4rpx"></text>
						</view>
					</view>
				</view>
			</view>
			<view style="height: 155rpx; width: 1rpx"></view>
		</view>

		<!-- <view class="item" v-for="(item, index) in historyList" :key="index">
			<view @tap="handleClick(index)" class="title">
				<view class="info-box">
					<view class="up-box">
						<text class="lg cuIcon-title text-green"></text>
						<view class="text-bold text-black">{{
              item.title || item.compositionTitle
            }}</view>
						<view class="cu-tag radius sm bg-blue light margin-left-sm">{{item.wenTi}}</view>
						<view class="cu-tag radius sm bg-green light margin-left-sm">{{
              item.compositionType
                ? CompositionType[item.compositionType]
                : "自主测评"
            }}</view>
					</view>
					<view class="down-box padding-left-xs">
						<view class="text-sm">{{ item.compositionBeginTime }}</view>
						<view class="sex margin-left-sm">测评次数：{{ item.detail.length }}</view>
						<view class="sex margin-left">最高分：{{item.maxScore}}</view>
					</view>
				</view>
				<text :class="item.open ? 'down cuIcon-fold' : 'up cuIcon-fold'" class="lg text-grey"></text>
			</view>
			展开的弹窗
			<view class="p_item margin-bottom" :style="{ maxHeight: item.open ? maxHeight : 0 }">
				<view @click="toDetails(item, subIndex)" class="sub-box" v-for="(sub, subIndex) in item.detail"
					:key="subIndex">
					<view class="up-box margin-top-sm margin-left">
						<view class="text-df text-bold">
							{{ sub.title }}
						</view>
					</view>
					<view class="down-box margin-top-sm margin-left text-sm text-grey">
						<view>提交时间：{{ sub.submitTime }}</view>
						<view style="margin-left: 50rpx">得分：{{ sub.score }}</view>
					</view>
				</view>
			</view>
		</view> -->

		<view class="page-box" v-if="false">
			<view>
				<view class="centre">
					<image src="https://cdn.zhoukaiwen.com/noData1.png" mode="widthFix"></image>
					<view class="explain">
						暂无数据
						<view class="tips">可以去看看其他板块</view>
					</view>
				</view>
			</view>
		</view>

		
	</view>
</template>

<script>
	import request from "@/common/request.js";
	import {
		CompositionEvaluateSourcesMap,
		CompositionEvaluateSourcesColor,
	} from "@/common/constants.js";
	import moment from "@/components/moment/index.js"; // 格式化时间 插件
	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				CompositionEvaluateSourcesMap,
				CompositionEvaluateSourcesColor,
				maxHeight: 0, //运动量展开那需要的高度
				userInfo: {},
				historyList: [],
				historyTotal: 0,
				searchInput: "",
			};
		},
		computed: {
			style() {
				const StatusBar = this.StatusBar;
				const CustomBar = this.CustomBar;
				const bgImage = this.bgImage;
				const style = `height:${CustomBar}px;padding-top:${StatusBar}px;`;
				if (this.bgImage) {
					style = `${style}background-image:url(${bgImage});`;
				}
				return style;
			},
		},
		mounted() {
			this.getList();
			this.getUserInfo();
		},
		filters: {
			timeF: function(time) {
				// let time = "1709130283826"
				let mm = Number(time);
				let ss = moment(mm).format("YYYY.MM.DD HH:mm");
				return ss;
			},
		},
		methods: {
			// 获取用户信息
			getUserInfo() {
				const userInfo = uni.getStorageSync("userInfo");
				this.userInfo = userInfo;
				console.log(this.$u);
			},
			getList() {
				const params = {
					page: 1,
					pageSize: 500,
					title: this.searchInput,
				};
				let opts = {
					url: `biz_composition_log/user_page`,
					method: "GET",
				};
				request.httpTokenRequest(opts, params).then((res) => {
					const data = res.data.records || [];
					this.historyTotal = res.data.total;
					this.historyList = data;
				});
			},

			getRect(context, selector) {
				return new Promise((resolve) => {
					const query = uni.createSelectorQuery().in(context);
					query
						.select(selector)
						.boundingClientRect((data) => {
							resolve(data);
						})
						.exec();
				});
			},
			handleClick(index) {
				this.historyList[index].open = !this.historyList[index].open;
				if (this.historyList[index].open == true) {
					for (var i = 0; i < this.historyList.length; i++) {
						if (i == index) {
							this.historyList[i].open = true;
						} else {
							this.historyList[i].open = false;
						}
					}
				}
				this.getRect(this, ".sub-box").then((res) => {
					// 加了20是因为每个item有一个margin-top：20，要算进去，不然就没间隔了，所以如果要是改动的话这块也要跟着改
					var elLength = this.historyList[index].detail.length;
					this.maxHeight =
						((parseInt(res.height) + 10) * elLength).toString() + "px";
				});
			},
			// toDetails(item) {
			//   uni.navigateTo({
			//     url: `/pages/me/trendDetail?id=${item.id}`,
			//   });
			// },
			goHistoryList(item) {
				uni.navigateTo({
					url: `/pages/me/historyList?id=${item.id}&title=${item.title}`,
				});
			},
		},
	};
</script>
<style lang="scss" scoped>
	.topBg {
		background-size: 100% 100%;
		/* background-size: cover; */
		height: 760rpx;
		width: 750rpx;
		overflow: hidden;
		flex-direction: column;
		padding: 20rpx;
		position: absolute;
		top: 0;
		left: 0;
	}

	.mainBox {
		width: 700rpx;
		position: absolute;
		left: 25rpx;
	}

	.inputBox {
		position: relative;

		.topInput {
			height: 78rpx;
			border-radius: 20rpx;
			padding: 0 20rpx;
			line-height: 78rpx;
			box-sizing: border-box;
		}

		.inputIcon {
			width: 42rpx;
			position: absolute;
			right: 30rpx;
			top: 16rpx;
			z-index: 999;
		}
	}

	.listBox {
		.listItem {
			padding: 25rpx 30rpx;
			border-radius: 15rpx;
			margin: 20rpx 0;
		}
	}

	.item {
		display: flex;
		flex-direction: column; //决定了下拉框在下面
		background-color: #fff;
		padding: 0 30rpx;

		.title {
			display: flex;
			flex-direction: row; //左部分是信息，右部分是箭头
			justify-content: space-between;

			.info-box {
				display: flex;
				flex-direction: column; //上部分是作文标题和文章类型，下部分是作文每次数据
				justify-content: flex-start;

				.up-box {
					display: flex;
					flex-direction: row;
					margin-top: 10rpx; //如果这里改了handleClick算高度那里也要改
					align-items: flex-end;

					.flag-box {
						margin-left: 15rpx;
						width: 58rpx;
						text-align: center;
						border-radius: 6rpx;
					}

					.run-box {
						font-size: 26rpx;
						margin-left: 25rpx;
					}

					.divider {
						margin-left: 25rpx;
						margin-bottom: 5rpx;
						background: #b7b9b3;
						width: 1rpx;
						height: 70%;
						opacity: 0.5;
					}
				}

				.down-box {
					display: flex;
					flex-direction: row;
					font-weight: 500;
					height: 40rpx;
					font-size: 24rpx;
					color: #acb1b5;
					margin-top: 12rpx;
				}
			}

			.up {
				margin-top: 40rpx;
				width: 20rpx;
				height: 20rpx;
				opacity: 0.8;
				transition: all ease 0.6s;
				transform: rotate(0deg);
			}

			.down {
				margin-top: 40rpx;
				width: 20rpx;
				height: 20rpx;
				opacity: 0.8;
				transition: all ease 0.6s;
				transform: rotate(-180deg);
			}
		}

		.sub-box {
			display: flex;
			flex-direction: column;
			background: #f6f6f6;
			border-radius: 20rpx;
			height: 120rpx;
			margin-top: 20rpx;

			.up-box {
				display: flex;
				flex-direction: row;
				align-items: flex-end;
				font-family: Roboto;
				font-weight: bold;

				.run-box {
					margin-left: 25rpx;
				}

				.divider {
					margin-left: 25rpx;
					margin-bottom: 5rpx;
					background: #b7b9b3;
					width: 1rpx;
					height: 100%;
					opacity: 0.5;
				}
			}

			.down-box {
				display: flex;
				flex-direction: row;
				height: 18px;
			}
		}
	}

	.p_item {
		overflow: hidden;
		transition: max-height 0.4s ease;
	}
</style>