<template>
  <view>
    <cu-custom bgColor="bg-gradual-blue" :isBack="true">
      <block slot="backText"></block>
      <block slot="content">个人动态</block>
    </cu-custom>

    <view class="cu-bar bg-white">
      <!-- 状态筛选 tab -->
      <scroll-view scroll-x class="nav text-center">
        <view class="flex text-center">
          <view
            class="cu-item flex-sub"
            :class="currentTab === index ? 'text-blue cur' : ''"
            v-for="(item, index) in tabList"
            :key="index"
            @tap="tabSelect(index)"
          >
            {{ item.name }}
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- padding-sm -->
    <view class="bg-white">
      <view
        @click="goDetail(item)"
        class="waresList solid-bottom"
        v-for="(item, index) in listData"
        :key="index"
      >
        <!-- <image
          class="waresImg radius"
          src="../../static/logo.jpg"
          mode="aspectFill"
        ></image> -->
        <view class="waresCont flex flex-direction justify-around">
          <text
            class="text-bold text-black text-df text-cut2"
            v-if="item.operationTitle"
            >作文主题：{{ item.operationTitle }}</text
          >
          <text
            class="text-black text-smdf text-cut2"
            v-if="item.compositionTitle"
            >作文题目：{{ item.compositionTitle }}</text
          >
          <view style="font-size: 24rpx; height: 36rpx">
            <view
              v-if="item.compositionWenTi"
              class="cu-tag bg-blue light radius margin-right-xs sm"
              >{{ item.compositionWenTi }}</view
            >
            <!-- <view class="cu-tag bg-blue light radius margin-right-xs sm">{{
              item.gradeName
            }}</view> -->
            <view
              :class="item.answerStatus == 0 ? 'bg-blue' : 'bg-blue'"
              class="cu-tag light radius margin-right-xs sm fr"
              >{{ item.answerStatus == 0 ? "仅评分" : "已提交" }}</view
            >
          </view>
          <view class="flex justify-between padding-right-sm">
            <text class="text-grey text-sm">{{ item.zw_name }}</text>
            <text class="text-grey text-sm">{{ item.submitTime }}</text>
          </view>
          <view>
            <view class="text-red text-bold">
              <text class="text-df">智能评分：</text>
              <text class="text-lg margin-right-xss">{{
                item.evaluateScore
              }}</text>
            </view>
          </view>
        </view>
      </view>

      <view class="page-box" v-if="listData.length <= 0">
        <view>
          <view class="centre">
            <image
              src="https://cdn.zhoukaiwen.com/noData1.png"
              mode="widthFix"
            ></image>
            <view class="explain">
              暂无提交记录
              <view class="tips">可以先去参与竞赛活动</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import request from "@/common/request.js";
import moment from "@/components/moment/index.js"; // 格式化时间 插件
const answerStatus = {
  0: "已评测",
  2: "已提交",
};
export default {
  data() {
    return {
      loginType: 0,
      openId: "",
      listData: [],
      currentTab: 0,
      tabList: [
        { name: "全部", value: null },
        { name: "仅评分", value: 0 },
        { name: "已提交", value: 1 },
      ],
    };
  },
  onLoad(option) {
    this.getUserInfo();
  },
  methods: {
    getUserInfo() {
      var that = this;
      uni.getStorage({
        key: "userInfo",
        success: function (res) {
          console.log(res.data);
          that.loginType = 1;
          that.openId = res.data.openId;
          that.getList(that.openId);
        },
        fail: function (err) {
          that.loginType = 0;
        },
      });
    },
    getList() {
      const params = { page: 1, pageSize: 100 };

      // 添加状态筛选
      const status = this.tabList[this.currentTab].value;
      if (status !== null) {
        params.answerStatus = status;
      }
      let opts = {
        url: `answer_record/recordPage`,
        method: "GET",
      };
      request.httpTokenRequest(opts, params).then((res) => {
        res.data.records.forEach((item) => {
          item.submitTime = moment(item.submitTime).format("YYYY.MM.DD HH:mm");
        });
        this.listData = res.data.records;
      });
    },
    goDetail(item) {
      uni.navigateTo({
        url: `/pages/me/trendDetail?id=${item.id}`,
      });
    },
    tabSelect(index) {
      this.currentTab = index;
      this.getList();
    },
  },
};
</script>

<style lang="scss" scoped>
.waresList {
  padding: 10rpx 0 20rpx 0;
  .waresImg {
    width: 230rpx;
    height: 230rpx;
    float: left;
    margin-right: 20rpx;
  }
  .waresCont {
    height: 230rpx;
  }
}
.centre {
  text-align: center;
  margin: 10rpx auto 50rpx;
  font-size: 32rpx;
  image {
    width: 300rpx;
    border-radius: 50%;
    margin: 0 auto;
  }
  .tips {
    font-size: 24rpx;
    color: #999999;
    margin-top: 20rpx;
  }
}
</style>
