<!-- 消息通知 -->
<template>
  <view class="container">
    <view
      class="topBg"
      style="background-image: url(https://cdn.zhoukaiwen.com/cewen_me_bg.png)"
    >
      <view class="cu-custom">
        <view
          class="fixed"
          :style="style"
          style="justify-content: center; align-content: center"
        >
          <view
            class="text-white text-bold text-lg text-center"
            style="position: relative"
            :style="[{ top: StatusBar + 'px' }]"
          >
            <text
              @click="goBack"
              class="cuIcon-back text-left"
              style="position: absolute; left: 0; top: 6rpx"
            ></text>
            <text class="">作业详情</text>
          </view>
        </view>
      </view>
    </view>

    <view class="mainBox" :style="[{ top: CustomBar + 10 + 'px' }]">
      <view
        class="bg-white margin-tb"
        style="border-radius: 20rpx; overflow: hidden"
      >
        <view class="cu-bar bg-white margin-top-xs">
          <view class="action sub-title">
            <text class="text-xl text-bold text-green3 text-shadow"
              >基本信息</text
            >
            <text class="text-ABC text-green3">jichuxinxi</text>
          </view>
        </view>
        <view class="base-info">
          <!-- 标题区域 -->
          <view class="text-black text-bold text-xl padding-lr-sm"
            >《{{ eventData.compositionTitle }}》</view
          >
          <!-- 标签组 -->
          <view class="tag-group padding-lr-sm">
            <view class="tag-item">
              <text class="cuIcon-title text-green3"></text>
              <text>{{ eventData.compositionWenTi }}</text>
            </view>
            <view class="tag-item">
              <text class="cuIcon-write text-green3"></text>
              <text>{{ eventData.compositionWordCount }}字</text>
            </view>
            <view class="tag-item">
              <text class="cuIcon-rank text-green3"></text>
              <text>满分{{ eventData.compositionFullScore }}分</text>
            </view>
          </view>

          <!-- 时间信息 -->
          <view class="time-info padding-lr">
            <view class="time-value">
              <text class="cuIcon-time text-green3"></text>
              <text>{{ eventData.compositionBeginTime | timeF }}</text>
              <text class="separator">至</text>
              <text>{{ eventData.compositionEndTime | timeF }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 作文要求说明 -->
      <view
        class="bg-white margin-tb"
        style="border-radius: 20rpx; overflow: hidden"
      >
        <view class="cu-bar bg-white margin-top-xs">
          <view class="action sub-title">
            <text class="text-xl text-bold text-green3 text-shadow"
              >作文要求</text
            >
            <text class="text-ABC text-green3">zuowenyaoqiu</text>
          </view>
        </view>
        <view class="padding bg-white" style="text-indent: 2em">
          <rich-text
            :nodes="eventData.compositionContent | formatRichText"
          ></rich-text>
        </view>
      </view>

      <!-- 作业资源区域 -->
      <view
        v-if="eventData.resourceUrl"
        class="bg-white margin-tb"
        style="border-radius: 20rpx; overflow: hidden"
      >
        <view class="cu-bar bg-white margin-top-xs">
          <view class="action sub-title">
            <text class="text-xl text-bold text-green3 text-shadow"
              >作业资源</text
            >
            <text class="text-ABC text-green3">zuoyeziyuan</text>
          </view>
        </view>
        <view class="padding bg-white">
          <view class="resource-list">
            <view
              class="resource-item"
              v-for="(item, index) in eventData.resourceUrl.split(',')"
              :key="index"
              @tap="openDocument(item)"
            >
              <view class="file-icon">
                <text class="cuIcon-file text-green3"></text>
              </view>
              <view class="file-info">
                <text class="file-name">{{ fileFun(item) }}</text>
                <text class="file-size text-gray">PDF文档</text>
              </view>
              <view class="download-btn">
                <text class="cuIcon-down"></text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 作业资源区域 -->
      <view
        v-if="eventData.compositionExamplePath"
        class="bg-white margin-tb"
        style="border-radius: 20rpx; overflow: hidden"
      >
        <!-- 作业参考区域 -->
        <view class="cu-bar bg-white margin-top-xs">
          <view class="action sub-title">
            <text class="text-xl text-bold text-green3 text-shadow"
              >作业参考</text
            >
            <text class="text-ABC text-green3">zuoyecankan</text>
          </view>
        </view>
        <view class="padding bg-white">
          <view class="reference-list">
            <view class="grid-container">
              <view
                class="grid-item"
                v-for="(img, index) in eventData.compositionExamplePath.split(
                  ','
                )"
                :key="index"
                @tap="
                  previewImage(
                    eventData.compositionExamplePath.split(','),
                    index
                  )
                "
              >
                <image :src="img" mode="aspectFill" />
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 参考资料下载 -->
      <view
        v-if="eventData.operationResourcePath"
        class="bg-white margin-tb"
        style="border-radius: 20rpx; overflow: hidden"
      >
        <view class="cu-bar bg-white margin-top-xs">
          <view class="action sub-title">
            <text class="text-xl text-bold text-green3 text-shadow"
              >参考资料</text
            >
            <text class="text-ABC text-green3">cankaoziliao</text>
          </view>
        </view>
        <view class="padding bg-white">
          <view class="download-list">
            <view
              class="download-item"
              v-for="(item, index) in eventData.operationResourcePath.split(
                ','
              )"
              :key="index"
              @tap="openDocument(item)"
            >
              <text class="cuIcon-file text-green3"></text>
              <text class="file-name">{{ fileFun(item) }}</text>
              <text class="cuIcon-right text-gray"></text>
            </view>
          </view>
        </view>
      </view>

      <!-- 历史测评记录 -->
      <view
        class="bg-white margin-tb"
        style="border-radius: 20rpx; overflow: hidden"
      >
        <view class="cu-bar bg-white margin-top-xs">
          <view class="action sub-title">
            <text class="text-xl text-bold text-green3 text-shadow"
              >历史测评记录</text
            >
            <text class="text-ABC text-green3">tongji</text>
          </view>
          <view class="padding-right text-grey text-sm"
            >历史测评次数：{{ historyTotal }}次</view
          >
        </view>
        <!-- 简化的历史记录列表 -->
        <view class="history-list">
          <view
            class="history-item"
            v-for="(item, index) in historyList"
            :key="index"
            @tap="toDetails(item)"
          >
            <view class="item-content">
              <view class="item-header">
                <text class="cuIcon-title text-green3"></text>
                <text class="item-title">{{ item.title }}</text>
              </view>
              <view class="item-info">
                <text class="item-time">{{ item.createTime | timeF }}</text>
                <text class="item-score">{{ item.score }}分</text>
              </view>
            </view>
            <text class="cuIcon-right text-gray"></text>
          </view>
        </view>
      </view>

      <view style="height: 140rpx; width: 1rpx"></view>
    </view>

    <!-- 底部操作栏 -->
    <view class="cu-bar bg-white tabbar border shop bottomBox solid-top">
      <button class="action" open-type="share">
        <view class="cuIcon-share text-green3">
          <view class="cu-tag badge">{{ eventData.shareNum }}</view>
        </view>
        分享
      </button>
      <!-- <button class="action" open-type="contact">
        <view class="cuIcon-service text-green3">
          <view class="cu-tag badge"></view>
        </view>
        联系我们
      </button> -->
      <view class="btn-group" v-if="loginType == 1">
        <button
          v-if="!isFinish"
          @click="toFinish()"
          style="width: 80%; height: 70rpx"
          class="cu-btn bg-green3 round shadow-blur"
        >
          去完成
        </button>
        <button
          v-else
          style="width: 80%; height: 70rpx"
          class="cu-btn bg-green3 round shadow-blur"
        >
          已完成
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import request from "@/common/request.js";
import moment from "@/components/moment/index.js"; // 格式化时间 插件
import { fileFun } from "@/common/utils.js";
import { CompositionType } from "@/common/constants.js";
export default {
  data() {
    return {
      CustomBar: this.CustomBar,
      CompositionType,
      fileFun,
      openId: "",
      loginType: 0,
      eventData: {},
      isLike: 0, // 是否点赞 0否，1已点赞
      userInfo: [],
      isJoin: false,

      // 历史记录
      historyList: [],
      historyTotal: 0,
      isFinish: false,
    };
  },
  computed: {
    style() {
      const StatusBar = this.StatusBar;
      const CustomBar = this.CustomBar;
      const bgImage = this.bgImage;
      const style = `height:${CustomBar}px;padding-top:${StatusBar}px;`;
      if (this.bgImage) {
        style = `${style}background-image:url(${bgImage});`;
      }
      return style;
    },
  },
  onLoad(option) {
    this.getDetailJob(option.id);
    this.getUserInfo();
    this.getList(option.id);
    this.isFinish = option.answerStatus == 2 ? true : false;
    console.log(this.isFinish, "this.isFinish", option);
  },
  onShareTimeline() {
    return {
      title: "作文智判慧改-分享",
      imageUrl: "https://cdn.zhoukaiwen.com/qdpz_share.jpg",
    };
  },
  methods: {
    getList(id) {
      const params = {
        page: 1,
        pageSize: 500,
        compositionId: id,
      };
      let opts = {
        url: `biz_composition_log/page`,
        method: "GET",
      };
      request.httpTokenRequest(opts, params).then((res) => {
        this.historyTotal = res.data.total;
        this.historyList = res.data.records || [];
      });
    },
    async toFinish() {
      uni.navigateTo({
        url: "/pages/job/upload-evaluation?id=" + this.eventData.id,
      });
    },
    getUserInfo() {
      var that = this;
      uni.getStorage({
        key: "userInfo",
        success: function (res) {
          console.log(res.data);
          that.loginType = 1;
          that.openId = res.data.openId;
          that.userInfo = res.data;
        },
        fail: function (err) {
          that.loginType = 0;
        },
      });
    },
    async getDetailJob(id) {
      uni.showLoading({
        title: "加载中",
      });
      try {
        const res = await request.httpTokenRequest({
          url: `biz_composition_release/${id}`,
          method: "GET",
        });
        this.eventData = res.data || {};
      } finally {
        uni.hideLoading();
      }
    },
    onShareAppMessage(options) {
      // 自定义分享内容
      var shareObj = {
        title: this.eventData.title, // 小程序的名称
        path: "/pages/home/<USER>", // 默认是当前页面，必须是以'/'开头的完整路径
        imageUrl: this.eventData.banner, //自定义图片路径，支持PNG及JPG，不传入 imageUrl 则使用默认截图。显示图片长宽比是 5:4
      };
      // 来自页面内的按钮的转发
      if (options.from == "button") {
        console.log("来源于button");
        // 此处可以修改 shareObj 中的内容
        // shareObj.path = '/pages/xxx/xxx?id='id;
        this.shareBtn();
      }
      // 返回shareObj
      return shareObj;
    },
    // 分享接口
    shareBtn() {
      uni.showLoading({
        title: "加载中",
      });
      let opts = {
        url: "api/event/share",
        method: "get",
      };
      let params = {
        eventId: this.eventData.id,
      };
      request.httpRequest(opts, params).then((res) => {
        console.log(res);
        uni.hideLoading();
        if (res.data.code == 200) {
          this.eventData.shareNum++;
        } else {
          console.log("数据请求错误～");
        }
      });
    },
    openDocument(url) {
      uni.downloadFile({
        url: url,
        success: (res) => {
          console.log("downloadFile success, res is", res);
          uni.openDocument({
            filePath: res.tempFilePath, // 指定文件路径
            showMenu: true,
            success(res) {
              console.log("成功打开文件");
            },
            fail(err) {
              console.error("打开文件失败", err);
            },
          });
        },
        fail: (err) => {
          console.log("downloadFile fail, err is:", err);
        },
      });
    },
    toDetails(item) {
      uni.navigateTo({
        url: `/pages/me/historyDetail?id=${item.id}`,
      });
    },
    goBack() {
      uni.navigateBack({
        delta: 1,
      });
    },
  },
  filters: {
    formatRichText(html) {
      //控制小程序中图片大小
      if (html != null) {
        let newContent = html.replace(
          /<img[^>]*>/gi,
          function (match, capture) {
            match = match
              .replace(/style="[^"]+"/gi, "")
              .replace(/style='[^']+'/gi, "");
            match = match
              .replace(/width="[^"]+"/gi, "")
              .replace(/width='[^']+'/gi, "");
            match = match
              .replace(/height="[^"]+"/gi, "")
              .replace(/height='[^']+'/gi, "");
            return match;
          }
        );
        newContent = newContent.replace(
          /style="[^"]+"/gi,
          function (match, capture) {
            match = match
              .replace(/width:[^;]+;/gi, "max-width:100%;")
              .replace(/width:[^;]+;/gi, "max-width:100%;");
            return match;
          }
        );
        newContent = newContent.replace(/<br[^>]*\/>/gi, "");
        newContent = newContent.replace(
          /\<img/gi,
          '<img style="max-width:100%;height:auto;display:inline-block;margin:10rpx auto;"'
        );

        return newContent;
      }
    },
    timeF: function (time) {
      // let time = "1709130283826"
      let mm = Number(time);
      let ss = moment(mm).format("YYYY.MM.DD HH:mm");
      return ss;
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 180rpx;
  position: relative;
}
.topBg {
  background-size: 100% 100%;
  /* background-size: cover; */
  height: 760rpx;
  width: 750rpx;
  overflow: hidden;
  flex-direction: column;
  padding: 20rpx;
  position: absolute;
  top: 0;
  left: 0;
}

.mainBox {
  width: 700rpx;
  position: absolute;
  left: 25rpx;
}

.cu-bar .action.sub-title text {
  z-index: 0 !important;
}

.info-card {
  margin: 20rpx;
  padding: 30rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .tag-group {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    .tag-item {
      display: flex;
      align-items: center;
      margin-right: 16rpx;

      .cuIcon-title,
      .cuIcon-write,
      .cuIcon-rank {
        margin-right: 8rpx;
      }

      text {
        font-size: 24rpx;
        color: #666;
      }
    }
  }

  .time-info {
    margin-top: 16rpx;

    .time-label {
      font-size: 24rpx;
      color: #999;
      margin-right: 8rpx;
    }

    .time-value {
      display: flex;
      align-items: center;
      color: #666;
      font-size: 28rpx;
      letter-spacing: 2rpx;

      .cuIcon-time {
        color: #2979ff;
        font-size: 32rpx;
        margin-right: 12rpx;
      }

      text {
        &:not(.separator) {
          margin: 0 4rpx;
        }
      }

      .separator {
        margin: 0 16rpx;
        color: #999;
      }
    }
  }
}

.content-card {
  margin: 20rpx;
  padding: 30rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

  .card-title {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;

    .cuIcon-title,
    .cuIcon-download {
      margin-right: 12rpx;
    }
  }

  .content-text {
    font-size: 28rpx;
    color: #666;
    line-height: 1.8;
  }
}

.download-list {
  .download-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #eee;

    &:last-child {
      border-bottom: none;
    }

    .cuIcon-file {
      margin-right: 12rpx;
      font-size: 36rpx;
    }

    .file-name {
      flex: 1;
      font-size: 28rpx;
      color: #333;
    }

    .download-btn {
      font-size: 24rpx;
      color: #2979ff;
      margin-left: 16rpx;
    }
  }
}

.footer-bar {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 20rpx 30rpx;
  background: #fff;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  z-index: 99;

  .action-group {
    display: flex;
    margin-right: 20rpx;

    .action-btn {
      background: none;
      padding: 0;
      margin: 0 30rpx 0 0;
      line-height: 1.5;
      font-size: 24rpx;
      color: #666;

      text {
        display: block;
        text-align: center;

        &.cuIcon-share,
        &.cuIcon-service {
          font-size: 40rpx;
          margin-bottom: 4rpx;
        }
      }
    }
  }

  .primary-btn {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    background: linear-gradient(120deg, #2979ff, #5cadff);
    border-radius: 40rpx;
    color: #fff;
    font-size: 30rpx;
    font-weight: 500;
    text-align: center;
    box-shadow: 0 4rpx 16rpx rgba(41, 121, 255, 0.3);

    &.disabled {
      background: #ccc;
      box-shadow: none;
    }

    &:active {
      opacity: 0.9;
    }
  }
}

.safe-area-inset-bottom {
  height: constant(safe-area-inset-bottom);
  height: env(safe-area-inset-bottom);
}

.reference-list {
  padding: 10rpx 0;

  .grid-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr); // 三列布局
    gap: 10rpx; // 稍微减小间距使其更紧凑
  }

  .grid-item {
    position: relative;
    width: 100%;
    border-radius: 6rpx;
    overflow: hidden;

    &::before {
      content: "";
      display: block;
      padding-top: 100%; // 1:1 比例，确保正方形
    }

    image {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover; // 保证图片填充且不变形
    }
  }
}

.bottomBox {
  width: 750rpx;
  position: fixed;
  left: 0;
  bottom: 0rpx;
}

.base-info {
  .tag-group {
    display: flex;
    align-items: center;
    margin: 20rpx 0;

    .tag-item {
      display: flex;
      align-items: center;
      margin-right: 20rpx;
      background: #f8f8f8;
      padding: 6rpx 16rpx;
      border-radius: 6rpx;

      .cuIcon-title,
      .cuIcon-write,
      .cuIcon-rank {
        font-size: 28rpx;
      }

      text {
        &:first-child {
          margin-right: 6rpx;
        }

        &:last-child {
          font-size: 26rpx;
          color: #666;
        }
      }
    }
  }

  .time-info {
    .time-value {
      display: flex;
      align-items: center;
      color: #666;
      font-size: 28rpx;
      letter-spacing: 2rpx;
      margin: 20rpx 0;

      .cuIcon-time {
        font-size: 32rpx;
        margin-right: 12rpx;
      }

      text {
        &:not(.separator) {
          margin: 0 4rpx;
        }
      }

      .separator {
        margin: 0 16rpx;
        color: #999;
      }
    }
  }
}

// 简化的历史记录列表样式
.history-list {
  padding: 0 30rpx;
}

.history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #f8f8f8;
  }
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;

  .cuIcon-title {
    font-size: 32rpx;
    margin-right: 16rpx;
  }

  .item-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }
}

.item-info {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .item-time {
    font-size: 26rpx;
    color: #999;
  }

  .item-score {
    font-size: 28rpx;
    color: #07c160;
    font-weight: 500;
  }
}

.cuIcon-right {
  font-size: 32rpx;
  color: #ccc;
}
</style>
