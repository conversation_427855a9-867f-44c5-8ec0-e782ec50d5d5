<!-- 消息通知 -->
<template>
  <view class="container">
    <view
      class="topBg"
      style="background-image: url(https://cdn.zhoukaiwen.com/cewen_me_bg.png)"
    >
      <view class="cu-custom">
        <view
          class="fixed"
          :style="style"
          style="justify-content: center; align-content: center"
        >
          <view
            class="text-white text-bold text-lg text-center"
            style="position: relative"
            :style="[{ top: StatusBar + 'px' }]"
          >
            <text
              @click="goBack"
              class="cuIcon-back text-left"
              style="position: absolute; left: 0; top: 6rpx"
            ></text>
            <text class="">范文详情</text>
          </view>
        </view>
      </view>
    </view>

    <view class="mainBox" :style="[{ top: CustomBar + 10 + 'px' }]">
      <!-- 基本信息区域 -->
      <view class="padding-lr padding-bottom padding-top-xs bg-white">
        <view class="base-info">
          <!-- 标题区域 -->
          <view class="title-section">
            <view class="main-title flex justify-between align-center">
              <text>{{ fwData.title }}</text>
              <view class="tag-group">
                <view class="tag-item">
                  <text style="color: #39b54a">{{ fwData.theme }}</text>
                </view>
              </view>
            </view>
            <view class="divider"></view>
          </view>

          <!-- 标签组 -->
          <view class="tag-group">
            <view class="tag-item">
              <text class="cuIcon-title text-green3"></text>
              <text>{{ fwData.articleType }}</text>
            </view>
            <view class="tag-item">
              <text class="cuIcon-write text-green3"></text>
              <text>{{ fwData.wordCount }}字</text>
            </view>
            <view class="tag-item">
              <text class="cuIcon-rank text-green3"></text>
              <text>{{ fwData.sort }}分</text>
            </view>
          </view>

          <view class="text-black margin-top" style="text-indent: 2em">{{
            fwData.content
          }}</view>

          <view class="text-grey flex align-center margin-top">
            <text class="cuIcon-info" style="font-size: 34rpx"></text>
            <text class="margin-left-xs"
              >此篇范文由{{ fwData.source }}自动生成</text
            >
          </view>
        </view>
      </view>
      <view class="safe-area-inset-bottom"></view>
    </view>

    <!-- 底部操作栏 -->
    <view class="cu-bar bg-white tabbar border shop bottomBox solid-top">
      <button class="action" open-type="share">
        <view class="cuIcon-share text-green3">
          <view class="cu-tag badge">{{ fwData.shareNum }}</view>
        </view>
        分享
      </button>
      <!-- <button class="action" open-type="contact">
				<view class="cuIcon-service text-green3">
					<view class="cu-tag badge"></view>
				</view>
				联系我们
			</button> -->
      <view class="btn-group">
        <button
          @click="goIndex"
          style="width: 80%; height: 70rpx"
          class="cu-btn bg-green3 round shadow-blur"
        >
          去测评
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import request from "@/common/request.js";
import moment from "@/components/moment/index.js"; // 格式化时间 插件
import { fileFun } from "@/common/utils.js";
import { CompositionType } from "@/common/constants.js";
export default {
  data() {
    return {
      CustomBar: this.CustomBar,
      CompositionType,
      fileFun,
      openId: "",
      loginType: 0,
      fwData: [],
      userInfo: [],
    };
  },
  computed: {
    style() {
      const StatusBar = this.StatusBar;
      const CustomBar = this.CustomBar;
      const bgImage = this.bgImage;
      const style = `height:${CustomBar}px;padding-top:${StatusBar}px;`;
      if (this.bgImage) {
        style = `${style}background-image:url(${bgImage});`;
      }
      return style;
    },
  },
  onLoad(option) {
    this.getDetailJob(option.id);
    this.getUserInfo();
  },
  onShareTimeline() {
    return {
      title: "作文智判慧改-分享",
      imageUrl: "https://cdn.zhoukaiwen.com/qdpz_share.jpg",
    };
  },
  methods: {
    getUserInfo() {
      var that = this;
      uni.getStorage({
        key: "userInfo",
        success: function (res) {
          console.log(res.data);
          that.loginType = 1;
          that.openId = res.data.openId;
          that.userInfo = res.data;
        },
        fail: function (err) {
          that.loginType = 0;
        },
      });
    },
    async getDetailJob(id) {
      uni.showLoading({
        title: "加载中",
      });
      try {
        const res = await request.httpTokenRequest({
          url: `bse_article/${id}`,
          method: "GET",
        });
        this.fwData = res.data || [];
      } finally {
        uni.hideLoading();
      }
    },
    onShareAppMessage(options) {
      // 自定义分享内容
      var shareObj = {
        title: this.fwData.title, // 小程序的名称
        path: "/pages/home/<USER>", // 默认是当前页面，必须是以'/'开头的完整路径
        imageUrl: this.fwData.banner, //自定义图片路径，支持PNG及JPG，不传入 imageUrl 则使用默认截图。显示图片长宽比是 5:4
      };
      // 来自页面内的按钮的转发
      if (options.from == "button") {
        console.log("来源于button");
        // 此处可以修改 shareObj 中的内容
        // shareObj.path = '/pages/xxx/xxx?id='id;
        this.shareBtn();
      }
      // 返回shareObj
      return shareObj;
    },
    // 分享接口
    shareBtn() {
      uni.showLoading({
        title: "加载中",
      });
      let opts = {
        url: "api/event/share",
        method: "get",
      };
      let params = {
        eventId: this.fwData.id,
      };
      request.httpRequest(opts, params).then((res) => {
        console.log(res);
        uni.hideLoading();
        if (res.data.code == 200) {
          this.fwData.shareNum++;
        } else {
          console.log("数据请求错误～");
        }
      });
    },
    openDocument(url) {
      uni.downloadFile({
        url: url,
        success: (res) => {
          console.log("downloadFile success, res is", res);
          uni.openDocument({
            filePath: res.tempFilePath, // 指定文件路径
            showMenu: true,
            success(res) {
              console.log("成功打开文件");
            },
            fail(err) {
              console.error("打开文件失败", err);
            },
          });
        },
        fail: (err) => {
          console.log("downloadFile fail, err is:", err);
        },
      });
    },
    goIndex() {
      console.log(111);
      uni.redirectTo({
        url: "/pages/home/<USER>",
      });
    },
    goBack() {
      uni.navigateBack({
        delta: 1,
      });
    },
  },
  filters: {
    formatRichText(html) {
      //控制小程序中图片大小
      if (html != null) {
        let newContent = html.replace(
          /<img[^>]*>/gi,
          function (match, capture) {
            match = match
              .replace(/style="[^"]+"/gi, "")
              .replace(/style='[^']+'/gi, "");
            match = match
              .replace(/width="[^"]+"/gi, "")
              .replace(/width='[^']+'/gi, "");
            match = match
              .replace(/height="[^"]+"/gi, "")
              .replace(/height='[^']+'/gi, "");
            return match;
          }
        );
        newContent = newContent.replace(
          /style="[^"]+"/gi,
          function (match, capture) {
            match = match
              .replace(/width:[^;]+;/gi, "max-width:100%;")
              .replace(/width:[^;]+;/gi, "max-width:100%;");
            return match;
          }
        );
        newContent = newContent.replace(/<br[^>]*\/>/gi, "");
        newContent = newContent.replace(
          /\<img/gi,
          '<img style="max-width:100%;height:auto;display:inline-block;margin:10rpx auto;"'
        );

        return newContent;
      }
    },
    timeF: function (time) {
      // let time = "1709130283826"
      let mm = Number(time);
      let ss = moment(mm).format("YYYY.MM.DD HH:mm");
      return ss;
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 180rpx;
  position: relative;
}
.topBg {
  background-size: 100% 100%;
  /* background-size: cover; */
  height: 760rpx;
  width: 750rpx;
  overflow: hidden;
  flex-direction: column;
  padding: 20rpx;
  position: absolute;
  top: 0;
  left: 0;
}

.mainBox {
  width: 700rpx;
  position: absolute;
  left: 25rpx;
}

.info-card {
  margin: 20rpx;
  padding: 30rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }

  .tag-group {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    .tag-item {
      display: flex;
      align-items: center;
      margin-right: 16rpx;

      .cuIcon-title,
      .cuIcon-write,
      .cuIcon-rank {
        margin-right: 8rpx;
      }

      text {
        font-size: 24rpx;
        color: #666;
      }
    }
  }
}

.content-card {
  margin: 20rpx;
  padding: 30rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

  .card-title {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;

    .cuIcon-title,
    .cuIcon-download {
      margin-right: 12rpx;
    }
  }

  .content-text {
    font-size: 28rpx;
    color: #666;
    line-height: 1.8;
  }
}

.download-list {
  .download-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #eee;

    &:last-child {
      border-bottom: none;
    }

    .cuIcon-file {
      margin-right: 12rpx;
      font-size: 36rpx;
    }

    .file-name {
      flex: 1;
      font-size: 28rpx;
      color: #333;
    }

    .download-btn {
      font-size: 24rpx;
      color: #2979ff;
      margin-left: 16rpx;
    }
  }
}

.footer-bar {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 20rpx 30rpx;
  background: #fff;
  box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  z-index: 99;

  .action-group {
    display: flex;
    margin-right: 20rpx;

    .action-btn {
      background: none;
      padding: 0;
      margin: 0 30rpx 0 0;
      line-height: 1.5;
      font-size: 24rpx;
      color: #666;

      text {
        display: block;
        text-align: center;

        &.cuIcon-share,
        &.cuIcon-service {
          font-size: 40rpx;
          margin-bottom: 4rpx;
        }
      }
    }
  }

  .primary-btn {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    background: linear-gradient(120deg, #2979ff, #5cadff);
    border-radius: 40rpx;
    color: #fff;
    font-size: 30rpx;
    font-weight: 500;
    text-align: center;
    box-shadow: 0 4rpx 16rpx rgba(41, 121, 255, 0.3);

    &.disabled {
      background: #ccc;
      box-shadow: none;
    }

    &:active {
      opacity: 0.9;
    }
  }
}

.safe-area-inset-bottom {
  height: constant(safe-area-inset-bottom);
  height: env(safe-area-inset-bottom);
}

.bottomBox {
  width: 750rpx;
  position: fixed;
  left: 0;
  bottom: 0rpx;
}

.base-info {
  .title-section {
    margin-bottom: 30rpx;

    .main-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      line-height: 1.4;
    }

    .divider {
      width: 120rpx;
      height: 6rpx;
      background: #07a069;
      border-radius: 3rpx;
      margin: 0rpx 0 20rpx 0;
    }
  }

  .tag-group {
    display: flex;
    align-items: center;
    margin: 20rpx 0;

    .tag-item {
      display: flex;
      align-items: center;
      margin-right: 20rpx;
      background: #f8f8f8;
      padding: 6rpx 16rpx;
      border-radius: 6rpx;

      .cuIcon-title,
      .cuIcon-write,
      .cuIcon-rank {
        font-size: 28rpx;
      }

      text {
        &:first-child {
          margin-right: 6rpx;
        }

        &:last-child {
          font-size: 26rpx;
          color: #666;
        }
      }
    }
  }
}
</style>
