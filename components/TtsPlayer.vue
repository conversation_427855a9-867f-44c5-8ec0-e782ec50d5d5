<!-- TTS播放组件 -->
<template>
  <view class="tts-player">
    <button
      class="play-btn"
      :class="{
        playing: isPlaying,
        paused: isPaused,
        loading: isLoading,
      }"
      @tap.stop="togglePlay"
      :disabled="isLoading"
    >
      <text class="cuIcon-notification"></text>
      <text>{{ currentText }}</text>
    </button>
  </view>
</template>

<script>
export default {
  name: "TtsPlayer",
  props: {
    // 需要朗读的文本内容
    message: {
      type: String,
      default: "",
      required: true,
    },
    // 每段文本的最大字符数
    segmentLength: {
      type: Number,
      default: 160,
    },
    // 预加载段数
    preloadCount: {
      type: Number,
      default: 2,
    },
  },
  data() {
    return {
      isPlaying: false,
      isPaused: false,
      isLoading: false,
      audioContext: null,
      apiUrl: "https://api.zhoukaiwen.com/api/tts/tencent",
      userId: "test1",
      password: "ry749DrZ",

      // 分段播放状态
      textSegments: [],
      currentSegmentIndex: 0,
      isProcessing: false,

      // 加载和缓存状态
      audioCache: new Map(),
      preloadQueue: [],
      isPreloading: false,
    };
  },
  computed: {
    // 计算当前按钮文本
    currentText() {
      if (this.isLoading) return "加载中";
      if (this.isPlaying) return "暂停";
      if (this.isPaused) return "继续";
      return "朗读";
    },
  },
  methods: {
    // 将文本分段
    splitText(text, maxLength = this.segmentLength) {
      if (!text) return [];

      const segments = [];
      let remainingText = text;

      while (remainingText.length > 0) {
        let segmentEnd = maxLength;

        // 如果还有剩余文本且长度超过最大长度
        if (remainingText.length > maxLength) {
          // 尝试在标点符号处断句，避免句子被截断
          const punctuationIndices = [
            remainingText.lastIndexOf("。", maxLength),
            remainingText.lastIndexOf("！", maxLength),
            remainingText.lastIndexOf("？", maxLength),
            remainingText.lastIndexOf("；", maxLength),
            remainingText.lastIndexOf(",", maxLength),
            remainingText.lastIndexOf("，", maxLength),
            remainingText.lastIndexOf(".", maxLength),
          ].filter((index) => index > 0 && index < maxLength);

          if (punctuationIndices.length > 0) {
            // 找到最靠近末尾的标点符号
            segmentEnd = Math.max(...punctuationIndices) + 1;
          }
        } else {
          segmentEnd = remainingText.length;
        }

        segments.push(remainingText.substring(0, segmentEnd));
        remainingText = remainingText.substring(segmentEnd);
      }

      return segments;
    },

    // 获取音频数据，支持缓存
    async fetchAudioData(text) {
      return new Promise((resolve, reject) => {
        // 检查缓存中是否已有此文本的音频数据
        if (this.audioCache.has(text)) {
          resolve(this.audioCache.get(text));
          return;
        }

        // 构建请求URL
        const url = `${this.apiUrl}?userId=${this.userId}&password=${
          this.password
        }&message=${encodeURIComponent(text)}`;

        // 发起请求
        uni.request({
          url: url,
          method: "GET",
          success: (res) => {
            if (res.statusCode === 200 && res.data.code === 200) {
              const base64Data = res.data.message;
              const filePath = `${
                wx.env.USER_DATA_PATH
              }/temp_audio_${Date.now()}_${Math.random()
                .toString(36)
                .substr(2, 9)}.wav`;

              // 将base64数据写入临时文件
              wx.getFileSystemManager().writeFile({
                filePath: filePath,
                data: wx.base64ToArrayBuffer(base64Data),
                encoding: "binary",
                success: () => {
                  // 保存到缓存
                  this.audioCache.set(text, filePath);
                  resolve(filePath);
                },
                fail: (err) => {
                  console.error("写入音频文件失败", err);
                  reject(err);
                },
              });
            } else {
              reject(new Error("接口返回错误"));
            }
          },
          fail: (err) => {
            reject(err);
          },
        });
      });
    },

    // 预加载音频
    async preloadAudio() {
      if (this.isPreloading || this.preloadQueue.length === 0) return;

      this.isPreloading = true;

      try {
        // 从队列中获取下一个要预加载的段落索引
        const nextIndex = this.preloadQueue.shift();
        if (nextIndex !== undefined && nextIndex < this.textSegments.length) {
          const segmentText = this.textSegments[nextIndex];

          // 检查是否已缓存
          if (!this.audioCache.has(segmentText)) {
            await this.fetchAudioData(segmentText);
            console.log(
              `预加载段落 ${nextIndex + 1}/${this.textSegments.length} 完成`
            );
          }
        }
      } catch (err) {
        console.error("预加载失败", err);
      } finally {
        this.isPreloading = false;

        // 继续处理队列中的下一个预加载请求
        if (this.preloadQueue.length > 0) {
          this.preloadAudio();
        }
      }
    },

    // 更新预加载队列
    updatePreloadQueue() {
      this.preloadQueue = [];
      const preloadStart = this.currentSegmentIndex + 1;
      const preloadEnd = Math.min(
        preloadStart + this.preloadCount - 1,
        this.textSegments.length - 1
      );

      for (let i = preloadStart; i <= preloadEnd; i++) {
        // 检查是否已经缓存
        if (!this.audioCache.has(this.textSegments[i])) {
          this.preloadQueue.push(i);
        }
      }

      // 开始预加载
      if (this.preloadQueue.length > 0 && !this.isPreloading) {
        this.preloadAudio();
      }
    },

    // 播放当前段落
    async playCurrentSegment() {
      if (
        this.textSegments.length === 0 ||
        !this.textSegments[this.currentSegmentIndex]
      ) {
        uni.showToast({
          title: "没有可播放的内容",
          icon: "none",
        });
        this.isPlaying = false;
        this.$emit("play-error", new Error("没有可播放的内容"));
        return;
      }

      this.isProcessing = true;

      if (this.currentSegmentIndex === 0) {
        // 第一段开始播放时触发play-start事件
        this.$emit("play-start");

        // 更新播放进度
        this.$emit("play-progress", {
          current: 1,
          total: this.textSegments.length,
          percentage: Math.round(
            (this.currentSegmentIndex / this.textSegments.length) * 100
          ),
        });
      }

      try {
        const segmentText = this.textSegments[this.currentSegmentIndex];
        let audioFilePath;

        // 尝试从缓存获取或请求新的音频数据
        try {
          audioFilePath = await this.fetchAudioData(segmentText);
        } catch (error) {
          console.error("获取音频失败，重试一次", error);
          // 重试一次
          audioFilePath = await this.fetchAudioData(segmentText);
        }

        // 如果之前有音频正在播放，先停止它
        if (this.audioContext) {
          this.audioContext.stop();
        }

        // 创建音频实例
        const innerAudioContext = uni.createInnerAudioContext();
        innerAudioContext.src = audioFilePath;

        // 监听播放完成事件
        innerAudioContext.onEnded(() => {
          console.log(
            `段落 ${this.currentSegmentIndex + 1}/${
              this.textSegments.length
            } 播放完成`
          );

          // 清理当前音频实例
          this.audioContext = null;

          // 播放下一段
          this.playNextSegment();
        });

        // 监听播放错误事件
        innerAudioContext.onError((err) => {
          console.error("音频播放错误", err);
          this.isPlaying = false;
          this.isProcessing = false;
          this.audioContext = null;
          this.$emit("play-error", err);
        });

        // 开始播放
        innerAudioContext.play();
        this.audioContext = innerAudioContext;
        this.isPlaying = true;
        this.isPaused = false;
        this.isProcessing = false;

        // 更新预加载队列
        this.updatePreloadQueue();
      } catch (err) {
        console.error("播放失败", err);
        uni.showToast({
          title: "播放失败",
          icon: "none",
        });
        this.isPlaying = false;
        this.isProcessing = false;
        this.$emit("play-error", err);
      }
    },

    // 播放下一段
    playNextSegment() {
      // 如果已经是最后一段，结束播放
      if (this.currentSegmentIndex >= this.textSegments.length - 1) {
        this.isPlaying = false;
        this.currentSegmentIndex = 0;
        this.textSegments = [];
        this.preloadQueue = [];
        this.$emit("play-end");
        return;
      }

      // 播放下一段
      this.currentSegmentIndex++;

      // 更新播放进度
      this.$emit("play-progress", {
        current: this.currentSegmentIndex + 1,
        total: this.textSegments.length,
        percentage: Math.round(
          (this.currentSegmentIndex / this.textSegments.length) * 100
        ),
      });

      // 更新预加载队列
      this.updatePreloadQueue();

      // 播放当前段落
      this.playCurrentSegment();
    },

    // 暂停播放
    pauseAudio() {
      if (this.audioContext && this.isPlaying) {
        this.audioContext.pause();
        this.isPaused = true;
        this.isPlaying = false;
      }
    },

    // 继续播放
    resumeAudio() {
      if (this.audioContext && this.isPaused) {
        this.audioContext.play();
        this.isPaused = false;
        this.isPlaying = true;
      }
    },

    // 播放音频
    async playAudio() {
      if (!this.message) {
        return uni.showToast({
          title: "没有可朗读的内容",
          icon: "none",
        });
      }

      // 如果处于暂停状态，则继续播放
      if (this.isPaused && this.audioContext) {
        this.resumeAudio();
        return;
      }

      // 避免重复播放
      if (this.isPlaying || this.isProcessing) {
        return;
      }

      // 设置加载状态
      this.isLoading = true;
      uni.showLoading({
        title: "加载中...",
      });

      // 重置状态
      this.currentSegmentIndex = 0;
      this.audioCache = new Map(); // 清空之前的缓存

      // 分段处理文本
      this.textSegments = this.splitText(this.message);

      if (this.textSegments.length === 0) {
        uni.hideLoading();
        uni.showToast({
          title: "没有可朗读的内容",
          icon: "none",
        });
        this.isLoading = false;
        return;
      }

      // 初始化预加载队列
      this.updatePreloadQueue();

      try {
        // 播放第一段
        await this.playCurrentSegment();
      } catch {
        // 出错时也要重置加载状态
        this.isLoading = false;
      } finally {
        // 完成后重置加载状态
        uni.hideLoading();
        this.isLoading = false;
      }
    },

    // 切换播放状态
    togglePlay() {
      if (this.isPlaying) {
        this.pauseAudio();
      } else if (this.isPaused) {
        this.resumeAudio();
      } else {
        this.playAudio();
      }
    },

    // 停止音频
    stopAudio() {
      if (this.audioContext) {
        this.audioContext.stop();
        this.audioContext = null;
      }

      this.isPlaying = false;
      this.isPaused = false;
      this.isProcessing = false;
      this.currentSegmentIndex = 0;
      this.textSegments = [];
      this.preloadQueue = [];
      this.$emit("play-stop");
    },

    // 清理缓存
    clearCache() {
      // 清理临时音频文件
      this.audioCache.forEach((filePath) => {
        try {
          wx.getFileSystemManager().unlinkSync(filePath);
        } catch (err) {
          console.warn("清理临时文件失败", err);
        }
      });
      this.audioCache.clear();
    },
  },
  // 组件销毁时停止播放并清理资源
  beforeDestroy() {
    if (this.isPlaying || this.isPaused) {
      this.stopAudio();
    }

    // 清理缓存
    this.clearCache();
  },
};
</script>

<style>
.tts-player {
  display: inline-block;
}

.play-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #21b287;
  color: #fff;
  border-radius: 50rpx;
  padding: 10rpx 20rpx;
  font-size: 26rpx;
  line-height: 1.5;
  transition: background-color 0.3s ease;
}

.play-btn.playing {
  background-color: #ff6b6b;
}

.play-btn.paused {
  background-color: #f39c12;
}

.play-btn.loading {
  background-color: #95a5a6;
  opacity: 0.7;
}

.play-btn[disabled] {
  opacity: 0.6;
  pointer-events: none;
}

.play-btn text {
  margin-right: 6rpx;
}
</style>
