# API 系统迁移指南

## 概述

本项目已经创建了一套新的、简化的 API 请求系统，用于替代原有的 `common/request.js`。新系统更加简单、快速、好用，并且支持用户信息缓存。

## 新系统结构

```
api/
├── http.js          # HTTP请求客户端
├── userApi.js       # 用户相关API
└── index.js         # 统一导出

constants/
└── api.js           # API常量定义

examples/
└── usage.js         # 使用示例
```

## 主要改进

### 1. 简化的 HTTP 客户端

- 集成认证和错误处理
- 自动 token 管理
- 401 自动跳转登录
- 统一的 loading 和错误提示

### 2. 用户信息缓存

- 内存缓存用户信息，提升性能
- 支持强制刷新
- 自动缓存更新

### 3. 更好的错误处理

- 统一的错误提示
- 网络错误自动处理
- 登录过期自动跳转

## 迁移步骤

### 1. 导入新 API

```javascript
// 旧的方式
import request from "@/common/request.js";

// 新的方式
import { httpClient, userAPI } from "@/api";
```

### 2. 替换请求方法

#### GET 请求

```javascript
// 旧的方式
let opts = {
  url: "school/list",
  method: "GET",
};
request.httpTokenRequest(opts).then((res) => {
  console.log(res.data);
});

// 新的方式
const result = await httpClient.get("school/list");
console.log(result.data);
```

#### POST 请求

```javascript
// 旧的方式
let opts = {
  url: "user/update",
  method: "POST",
};
request.httpTokenRequest(opts, userData).then((res) => {
  console.log(res.data);
});

// 新的方式
const result = await httpClient.post("user/update", userData);
console.log(result.data);
```

### 3. 用户相关操作

#### 获取用户信息

```javascript
// 旧的方式
let opts = {
  url: "auth/user_info",
  method: "GET",
};
request.httpTokenRequest(opts).then((res) => {
  this.userInfo = res.data;
});

// 新的方式
const result = await userAPI.getUserInfo();
this.userInfo = result.data;

// 或者从缓存获取（同步方法）
this.userInfo = userAPI.getLocalUserInfo();
```

#### 检查登录状态

```javascript
// 旧的方式
uni.getStorage({
  key: "userInfo",
  success: function (res) {
    // 已登录
  },
  fail: function (err) {
    // 未登录
  },
});

// 新的方式
if (userAPI.isLoggedIn()) {
  // 已登录
} else {
  // 未登录
}

// 强制要求登录
if (userAPI.requireLogin()) {
  // 已登录，继续执行
} else {
  // 未登录，已自动跳转到登录页
}
```

### 4. 页面初始化

#### 旧的方式

```javascript
onLoad() {
  uni.getStorage({
    key: "userInfo",
    success: function (res) {
      that.userInfo = res.data;
    },
  });
}
```

#### 新的方式

```javascript
onLoad() {
  // 初始化用户缓存
  userAPI.initUserCache();

  // 检查登录状态
  if (userAPI.isLoggedIn()) {
    // 获取用户信息（优先从缓存）
    this.userInfo = userAPI.getLocalUserInfo();
  }
}
```

## 已迁移的页面

### 1. 充值页面 (`pages/me/APay.vue`)

- ✅ 使用新的 HTTP 客户端
- ✅ 用户信息缓存
- ✅ 登录状态检查
- ✅ 统一错误处理

### 2. 个人中心页面 (`pages/home/<USER>

- ✅ 用户信息缓存
- ✅ 登录状态检查
- ✅ 需要登录的功能自动检查
- ✅ 退出登录功能

## 配置选项

### HTTP 请求选项

```javascript
const result = await httpClient.post("api/endpoint", data, {
  needAuth: false, // 是否需要认证，默认true
  showLoading: true, // 是否显示loading，默认true
  loadingText: "加载中...", // loading文字
  showError: true, // 是否显示错误提示，默认true
});
```

### 用户信息获取选项

```javascript
// 优先从缓存获取
const userInfo = await userAPI.getUserInfo();

// 强制从服务器刷新
const userInfo = await userAPI.getUserInfo(true);

// 专门的刷新方法
const userInfo = await userAPI.refreshUserInfo();
```

## 兼容性

- 新系统与原有的 `common/request.js` 完全兼容
- 可以逐步迁移，不需要一次性替换所有页面
- 支持 Vue2 和 uniapp

## 注意事项

1. **初始化缓存**：在应用启动或页面加载时调用 `userAPI.initUserCache()`
2. **错误处理**：新系统已经内置了错误处理，通常不需要额外的 try-catch
3. **登录检查**：使用 `userAPI.requireLogin()` 可以自动处理未登录的情况
4. **缓存更新**：所有用户信息更新操作都会自动更新缓存

## 响应式用户信息更新

新系统支持全局的用户信息更新通知，当用户信息发生变化时（如充值成功、更新资料），所有监听的页面都会自动更新。

### 使用方式

#### 1. 在页面中监听用户信息更新

```javascript
export default {
  mounted() {
    // 监听用户信息更新事件
    uni.$on("userInfoUpdated", this.handleUserInfoUpdate);
  },

  beforeDestroy() {
    // 移除事件监听，避免内存泄漏
    uni.$off("userInfoUpdated", this.handleUserInfoUpdate);
  },

  methods: {
    handleUserInfoUpdate(userInfo) {
      console.log("用户信息已更新:", userInfo);

      // 更新页面数据
      this.userInfo = userInfo;
      this.evaluationCount = userInfo.evaluationNumber || 0;

      // 显示更新提示
      uni.showToast({
        title: "信息已同步更新",
        icon: "success",
      });
    },
  },
};
```

#### 2. 触发用户信息更新

```javascript
// 充值成功后自动触发
await userAPI.refreshUserInfo(); // 会自动触发 userInfoUpdated 事件

// 更新用户资料后自动触发
await userAPI.updateUserInfo(newUserInfo); // 会自动触发 userInfoUpdated 事件

// 手动触发（用于测试）
userAPI.triggerUserInfoUpdate();
```

### 自动触发场景

- 充值成功后
- 更新用户资料后
- 微信登录/注册后
- 手动刷新用户信息后

## 性能优化

1. **内存缓存**：用户信息优先从内存获取，减少本地存储读取
2. **智能刷新**：支持强制刷新和缓存优先两种模式
3. **减少请求**：避免重复的用户信息请求
4. **响应式更新**：全局事件通知，多页面自动同步用户信息

## 下一步

建议按以下顺序迁移剩余页面：

1. 登录注册相关页面
2. 用户资料相关页面
3. 业务功能页面
4. 其他工具页面

每个页面迁移后都应该测试：

- 登录状态检查
- 用户信息获取
- 错误处理
- 缓存功能
