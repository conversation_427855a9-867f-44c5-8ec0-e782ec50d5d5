<!-- 个人中心 -->
<template>
  <view class="container">
    <!-- 顶部背景 -->
    <view
      class="topBg"
      style="background-image: url(https://cdn.zhoukaiwen.com/cewen_me_bg.png)"
    >
      <view>
        <view style="height: 160rpx"></view>
        <view class="bg-img" @click="goProfile">
          <view
            class="cu-avatar round xl margin-left bg-white"
            style="
              background-image: url(https://cdn.zhoukaiwen.com/tabbar_center.svg);
            "
          >
            <view class="headEdit">
              <text class="cuIcon-edit text-grey"></text>
            </view>
          </view>
          <view class="userBox">
            <view class="userName text-white"
              >你好，{{
                loginType == 0 ? "请点击登录" : userData.nickName
              }}</view
            >
            <view class="userzl text-white margin-top-sm text-dflg">
              <text class="cuIcon-home text-white margin-right-xs"></text>
              <text style="letter-spacing: 1rpx">{{
                userData.schoolName
              }}</text>
            </view>
            <view
              class="userzl text-white margin-top-xs text-dflg"
              style="letter-spacing: 1rpx"
            >
              <text class="cuIcon-vip text-white margin-right-xs"></text>
              <text>{{ userData.gradeName }}</text>
              <text style="margin: 0 20rpx; opacity: 0.7">|</text>
              <text>{{ userData.clazzName }}</text>
            </view>
          </view>
        </view>

        <view class="flex justify-end padding-right-sm text-black">
          <view
            @click="goNews"
            class="topsBox topsBox1 bg-white margin-right text-xl"
          >
            <text class="cuIcon-notice"></text>
            <!-- <view class="topsTag">3</view> -->
          </view>
          <view
            @click="goProfile"
            class="topsBox topsBox2 bg-white"
            style="z-index: 999"
            >我的资料</view
          >
        </view>

        <view class="flex justify-between padding-lr padding-top-lg">
          <view class="bg-white topNumBox">
            <image
              src="/static/me_top1.png"
              mode="widthFix"
              style="width: 80rpx"
            ></image>
            <view class="padding-left-sm">
              <view
                class="text-xxl text-bold"
                style="color: #07a069; margin-bottom: 2rpx"
              >
                {{ userData.evaluationNumber }}
              </view>
              <view class="text-smdf text-grey">剩余测评次数</view>
            </view>
          </view>
          <view class="bg-white topNumBox">
            <image
              src="/static/me_top2.png"
              mode="widthFix"
              style="width: 80rpx"
            ></image>
            <view class="padding-left-sm">
              <view
                class="text-xxl text-bold"
                style="color: #f67518; margin-bottom: 2rpx"
              >
                {{ userData.microLessonsNumber }}
              </view>
              <view class="text-smdf text-grey">剩余微课次数</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view
      class="cu-list menu card-menu margin-top-lg margin-bottom-sm bg-white text-black my-radius sm-border"
      style="margin-top: -130rpx"
    >
      <view class="cu-item" @click="goAPay">
        <button class="content cu-btn">
          <image
            src="/static/me_icon_tb1.svg"
            class="png"
            mode="aspectFit"
          ></image>
          <text class="text-lg margin-sm">充值管理</text>
        </button>
      </view>

      <view class="cu-item">
        <button class="content cu-btn" open-type="feedback">
          <image
            src="/static/me_icon_tb3.svg"
            class="png"
            mode="aspectFit"
          ></image>
          <text class="text-lg margin-sm">问题反馈</text>
        </button>
      </view>

      <view class="cu-item" @click="goAboutUs">
        <button class="content cu-btn">
          <image
            src="/static/me_icon_tb4.svg"
            class="png"
            mode="aspectFit"
          ></image>
          <text class="text-lg margin-sm">关于我们</text>
        </button>
      </view>
    </view>

    <view class="padding" @click="LogOut">
      <button
        class="cu-btn radius lg bg-white bg-grey text-center text-gray"
        style="width: 100%; height: 88rpx"
      >
        退出登录
      </button>
    </view>
    <official-account></official-account>

    <view style="height: 110rpx; width: 1rpx"></view>
  </view>
</template>

<script>
import request from "@/common/request.js";
export default {
  data() {
    return {
      loginType: 0, //0未登录，1已登录
      userData: [],
      isPay: "0", //购买或消费数量
      score: "0", //积分
      num_dh: "0", //兑换次数
      isCarpool: "0", //发布拼车数量
    };
  },
  mounted() {
    this.getData();
  },
  onPageShow() {
    this.getData();
  },
  methods: {
    getData() {
      var that = this;
      uni.getStorage({
        key: "userInfo",
        success: function (res) {
          console.log(res.data);
          that.loginType = 1;
          that.userData = res.data;
          that.score = res.data.score;
          that.num_dh = res.data.num_dh;
        },
        fail: function (err) {
          that.loginType = 0;
          uni.hideLoading();
        },
      });
    },
    //拨打固定电话
    callPhoneNumber() {
      uni.makePhoneCall({
        phoneNumber: "18629591093",
      });
    },
    // 签到打卡
    goClockIn() {
      uni.navigateTo({
        url: "../me/clockIn",
      });
    },
    // 我的积分
    goPoints() {
      uni.navigateTo({
        url: "../me/points",
      });
    },
    goAddressList() {
      uni.navigateTo({
        url: "../me/addressList",
      });
    },
    goProfile() {
      console.log("goProfile");
      uni.navigateTo({
        url: "../user/profile",
      });
    },
    goNews() {
      uni.navigateTo({
        url: "../me/news",
      });
    },
    goTrend() {
      uni.navigateTo({
        url: "/pages/me/trend",
      });
    },
    goAPay() {
      uni.navigateTo({
        url: "/pages/me/APay",
      });
    },
    // 关于作者
    goAboutUs() {
      uni.navigateTo({
        url: "/pages/me/aboutUs",
      });
    },
    goOrder() {
      uni.navigateTo({
        url: "../me/order",
      });
    },
    // 未开放
    FunClose() {
      uni.showToast({
        title: "正在开发中",
        icon: "none",
        duration: 3000,
      });
    },
    goTicketAleady() {
      uni.navigateTo({
        url: "../other/skiTicket/already",
      });
    },
    myScoring() {
      uni.showToast({
        title: "程序猿小哥哥偷偷去滑雪了，正在抓捕中...",
        icon: "none",
        duration: 3000,
      });
    },
    showImg() {
      uni.previewImage({
        urls: ["https://cdn.zhoukaiwen.com/lehuahaibao01.jpg"],
        longPressActions: {
          itemList: ["发送给朋友", "保存图片", "收藏"],
          success: function (data) {},
          fail: function (err) {},
        },
      });
    },
    LogOut() {
      uni.showModal({
        title: "退出登录",
        content: "确定要退出登录吗？",
        cancelText: "取消",
        confirmText: "确认",
        success: (res) => {
          if (res.confirm) {
            this.logOutFun();
          }
        },
      });
    },
    logOutFun() {
      uni.clearStorageSync();
      uni.reLaunch({
        url: "/pages/user/login",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.me-top-icon {
  width: 60rpx;
  height: 60rpx;
  margin: 0 auto;
}
.topBg {
  background-size: 100% 100%;
  /* background-size: cover; */
  height: 760rpx;
  overflow: hidden;
  position: relative;
  flex-direction: column;
  padding: 20rpx;
}
.bg-img {
  position: relative;
  display: flex;
  justify-content: start;
}
.cu-avatar.xl {
  width: 150rpx;
  height: 150rpx;
  font-size: 2.5em;
}
.headEdit {
  width: 48rpx;
  height: 48rpx;
  position: absolute;
  bottom: -10rpx;
  right: -0rpx;
  background-color: #fff;
  font-size: 32rpx;
  text-align: center;
  line-height: 48rpx;
  border-radius: 10rpx;
}
.userBox {
  padding: 10rpx 0rpx 0rpx 40rpx;
  .userName {
    font-size: 38rpx;
    letter-spacing: 3rpx;
    font-weight: 550;
  }
}
.topsBox {
  height: 58rpx;
  border-radius: 10rpx;
  line-height: 58rpx;
  margin-top: 25rpx;
}
.topsBox1 {
  padding: 0 14rpx;
  position: relative;
  .topsTag {
    position: absolute;
    width: 35rpx;
    height: 35rpx;
    background-color: #ff435a;
    text-align: center;
    line-height: 38rpx;
    font-size: 28rpx;
    border-radius: 20rpx;
    color: #fff;
    top: -18rpx;
    right: -14rpx;
  }
}
.topsBox2 {
  padding: 0 20rpx;
}
.topNumBox {
  width: 48%;
  height: 120rpx;
  border-radius: 25rpx;
  padding: 20rpx;
  display: flex;
  justify-content: left;
  align-content: center;
  align-items: center;
}

// 隔断

// 头像
.cu-avatar2 {
  font-variant: small-caps;
  margin: 0;
  padding: 0;
  display: inline-flex;
  text-align: center;
  justify-content: center;
  align-items: center;
  background: #ccc;
  color: #fff;
  white-space: nowrap;
  position: relative;
  width: 150rpx;
  height: 150rpx;
  background-size: cover;
  background-position: center;
  vertical-align: middle;
  font-size: 1.5em;
  z-index: 99;
}

.name {
  text-shadow: 2px 2px 1px #2f9bfe;
}

.dialog2 {
  background: none;
}

.saicode {
  background-size: 100% 100%;
  -moz-background-size: 100% 100%;
}

.img-big image {
  top: -40px;
  width: 280rpx;
  height: 280rpx;
}

.shadow-me {
  box-shadow: 0rpx 0rpx 100rpx 0rpx rgba(0, 0, 0, 0.1);
}

.tn-footerfixed {
  position: fixed;
  width: 100%;
  bottom: 0;
  z-index: 1024;
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.35);
}

.my_logo {
  background: none;
  padding: 50rpx 0 30rpx 0;
}

.my-radius {
  border-radius: 12rpx;
  overflow: hidden;
}

.my-icon image {
  width: 100rpx;
  height: 100rpx;
  display: inline-block;
  margin: 0 auto;
}

.my-iconcolor {
  background: rgba(255, 255, 255, 0.96);
}

.shadow-shop {
  box-shadow: 0rpx 0rpx 90rpx 0rpx rgba(0, 0, 0, 0.1);
}

.qrcode-img {
  position: fixed;
  width: 80rpx;
  height: 80rpx;
  bottom: 350rpx;
  right: 30rpx;
  z-index: 1024;
  opacity: 0.8;
  box-shadow: 0rpx 8rpx 30rpx 0rpx rgba(0, 0, 0, 0.3);
  border: none;
}

.bg-product {
  background-image: linear-gradient(rgba(0, 0, 0, 0.06), rgba(0, 0, 0, 0));
  color: #fff;
}

.margin-bottom-my {
  margin-bottom: 150rpx;
}

.giteeClass {
  margin-top: 30rpx;
  font-size: 34rpx;
  color: #2440b3;
  text-decoration: underline;
}

.cu-dialog {
  background: #ffffff;
  overflow: visible;
}

.modal_bg {
  width: 100%;
  height: 400rpx;
  position: absolute;
  top: -100rpx;
  background-image: url(https://zhoukaiwen.com/img/qdpz/modal_bg.png);
  background-size: 100%;
  background-repeat: no-repeat;
}

.modal_main {
  background-color: #ffffff;
}

/* 主题色 */
/**/
@keyframes t {
  0%,
  10%,
  80%,
  100% {
    top: -30px;
  }

  20% {
    top: 0px;
  }

  30% {
    top: -20px;
  }

  40% {
    top: -0px;
  }

  50% {
    top: -25px;
  }

  70% {
    top: 0px;
  }
}

@keyframes b {
  0%,
  10%,
  80%,
  100% {
    bottom: -30px;
  }

  20% {
    bottom: 0px;
  }

  30% {
    bottom: -20px;
  }

  40% {
    bottom: -0px;
  }

  50% {
    bottom: -25px;
  }

  70% {
    bottom: 0px;
  }
}

@keyframes mouth {
  0%,
  10%,
  100% {
    width: 100%;
    height: 0%;
  }

  15% {
    width: 90%;
    height: 30%;
  }

  20% {
    width: 50%;
    height: 70%;
  }

  25% {
    width: 70%;
    height: 70%;
  }

  30% {
    width: 80%;
    height: 60%;
  }

  35% {
    width: 60%;
    height: 70%;
  }

  40% {
    width: 55%;
    height: 75%;
  }

  45% {
    width: 50%;
    height: 90%;
  }

  50% {
    width: 40%;
    height: 70%;
  }

  55% {
    width: 70%;
    height: 95%;
  }

  60% {
    width: 40%;
    height: 50%;
  }

  65% {
    width: 100%;
    height: 60%;
  }

  70% {
    width: 100%;
    height: 70%;
  }

  75% {
    width: 90%;
    height: 70%;
  }

  80% {
    width: 50%;
    height: 70%;
  }

  85% {
    width: 90%;
    height: 50%;
  }

  85% {
    width: 40%;
    height: 70%;
  }

  90% {
    width: 90%;
    height: 30%;
  }

  95% {
    width: 100%;
    height: 10%;
  }
}

@keyframes tongue {
  0%,
  20%,
  100% {
    bottom: -80px;
  }

  30%,
  90% {
    bottom: -40px;
  }

  40% {
    bottom: -45px;
  }

  50% {
    bottom: -50px;
  }

  70% {
    bottom: -80px;
  }

  90% {
    bottom: -40px;
  }
}

// 顶部流星
.space {
  position: absolute;
  top: 0;
  left: 0;
}

.planet {
  width: 150rpx;
  height: 150rpx;
  border-radius: 50%;
  background: #333;
  position: absolute;
  left: 50%;
  top: 50%;
  margin: -75rpx 0 0 -75rpx;
  overflow: hidden;
  z-index: 2;
}

.planet_shadow {
  position: absolute;
  border-radius: 50%;
  height: 100%;
  width: 100%;
  top: -40%;
  right: -10%;
  border: 35rpx solid rgba(0, 0, 0, 0.15);
}

.star {
  display: block;
  width: 5rpx;
  height: 5rpx;
  border-radius: 50%;
  background: #fff;
  top: 100rpx;
  left: 400rpx;
  position: relative;
  transform-origin: 100% 0;
  animation: star-ani 6s infinite ease-out;
  box-shadow: 0 0 5rpx 5rpx rgba(255, 255, 255, 0.3);
  opacity: 0;
  z-index: 2;
}

.star:after {
  content: "";
  display: block;
  top: 0rpx;
  left: 4rpx;
  border: 0rpx solid #fff;
  border-width: 0rpx 90rpx 2rpx 90rpx;
  border-color: transparent transparent transparent rgba(255, 255, 255, 0.3);
  transform: rotate(-45deg) translate3d(1rpx, 3rpx, 0);
  box-shadow: 0 0 1rpx 0 rgba(255, 255, 255, 0.1);
  transform-origin: 0% 100%;
  animation: shooting-ani 3s infinite ease-out;
}

.pink {
  top: 30rpx;
  left: 395rpx;
  background: #ff5a99;
  animation-delay: 5s;
  -webkit-animation-delay: 5s;
  -moz-animation-delay: 5s;
}

.pink:after {
  border-color: transparent transparent transparent #ff5a99;
  animation-delay: 5s;
  -webkit-animation-delay: 5s;
  -moz-animation-delay: 5s;
}

.blue {
  top: 35rpx;
  left: 432rpx;
  background: cyan;
  animation-delay: 7s;
  -webkit-animation-delay: 7s;
  -moz-animation-delay: 7s;
}

.blue:after {
  /* border-color: transpareanimation-delay: 12s; */
  -webkit-animation-delay: 7s;
  -moz-animation-delay: 7s;
  animation-delay: 7s;
}

.yellow {
  top: 50rpx;
  left: 600rpx;
  background: #ffcd5c;
  animation-delay: 5.8s;
}

.yellow:after {
  border-color: transparent transparent transparent #ffcd5c;
  animation-delay: 5.8s;
}

@keyframes star-ani {
  0% {
    opacity: 0;
    transform: scale(0) rotate(0) translate3d(0, 0, 0);
    -webkit-transform: scale(0) rotate(0) translate3d(0, 0, 0);
    -moz-transform: scale(0) rotate(0) translate3d(0, 0, 0);
  }

  50% {
    opacity: 1;
    transform: scale(1) rotate(0) translate3d(-200rpx, 200rpx, 0);
    -webkit-transform: scale(1) rotate(0) translate3d(-200rpx, 200rpx, 0);
    -moz-transform: scale(1) rotate(0) translate3d(-200rpx, 200rpx, 0);
  }

  100% {
    opacity: 0;
    transform: scale(1) rotate(0) translate3d(-300rpx, 300rpx, 0);
    -webkit-transform: scale(1) rotate(0) translate3d(-300rpx, 300rpx, 0);
    -moz-transform: scale(1) rotate(0) translate3d(-300rpx, 300rpx, 0);
  }
}
// 弹窗
.cu-dialog {
  background: #ffffff;
  overflow: visible;
}

.modal_bg {
  width: 100%;
  height: 400rpx;
  position: absolute;
  top: -100rpx;
  background-image: url(https://zhoukaiwen.com/img/qdpz/modal_bg.png);
  background-size: 100%;
  background-repeat: no-repeat;
}

.modal_main {
  text-align: left;
  background-color: #ffffff;
}

.title-header {
  position: absolute;
  bottom: 0;
  width: 100%;
  display: flex;
  height: 120rpx;
  font-size: 40rpx;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  background-image: url(https://cdn.zhoukaiwen.com/wccswF.png);
  background-size: cover;
}

.title-text {
  background-image: -webkit-linear-gradient(0deg, #ff8a34, #fbbd12);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>
