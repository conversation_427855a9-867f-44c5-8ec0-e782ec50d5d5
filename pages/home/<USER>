<template>
  <view>
    <index v-if="PageCur == 'index'"></index>
    <videozw v-if="PageCur == 'videozw'"></videozw>
    <cases v-if="PageCur == 'cases'"></cases>
    <history v-if="PageCur == 'history'"></history>
    <me v-if="PageCur == 'me'"></me>

    <view class="box">
      <view class="cu-bar tabbar bg-white shadow foot">
        <view class="action" @click="NavChange" data-cur="index">
          <view class="cuIcon-cu-image">
            <image
              v-if="PageCur == 'index'"
              src="../../static/tabBar/index_cur.png"
            ></image>
            <image
              v-if="PageCur != 'index'"
              src="../../static/tabBar/index.png"
            ></image>
          </view>
          <view
            :class="PageCur == 'index' ? 'color_main text-bold' : 'text-gray'"
            >首页</view
          >
        </view>

        <view class="action" @click="NavChange" data-cur="videozw">
          <view class="cuIcon-cu-image">
            <image
              v-if="PageCur == 'videozw'"
              src="../../static/tabBar/shop_cur.png"
            ></image>
            <image
              v-if="PageCur != 'videozw'"
              src="../../static/tabBar/shop.png"
            ></image>
          </view>
          <view
            :class="PageCur == 'videozw' ? 'color_main text-bold' : 'text-gray'"
            >作文微课</view
          >
        </view>

        <view
          @click="NavChange"
          class="action text-gray add-action"
          data-cur="cases"
        >
          <image
            class="logo_btn"
            mode="widthFix"
            src="../../static/tabbar_center.svg"
          ></image>
          <view
            :class="PageCur == 'cases' ? 'color_main text-bold' : 'text-gray'"
            >开始测评</view
          >
        </view>

        <view class="action" @click="NavChange" data-cur="history">
          <view class="cuIcon-cu-image">
            <image
              v-if="PageCur == 'history'"
              src="../../static/tabBar/order_cur.png"
            ></image>
            <image
              v-if="PageCur != 'history'"
              src="../../static/tabBar/order.png"
            ></image>
          </view>
          <view
            :class="PageCur == 'history' ? 'color_main text-bold' : 'text-gray'"
            >历史记录</view
          >
        </view>

        <view class="action" @click="NavChange" data-cur="me">
          <view class="cuIcon-cu-image">
            <!-- 红点 -->
            <!-- <view class="cu-tag badge"></view> -->
            <image
              v-if="PageCur == 'me'"
              src="../../static/tabBar/me_cur.png"
            ></image>
            <image
              v-if="PageCur != 'me'"
              src="../../static/tabBar/me.png"
            ></image>
          </view>
          <view :class="PageCur == 'me' ? 'color_main text-bold' : 'text-gray'"
            >个人中心</view
          >
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import index from "./inedx.vue"; //首页
import cases from "./job.vue"; //我的作业
import history from "./history.vue"; //历史记录
import videozw from "./videozw.vue"; //作文微课
import me from "./me.vue"; //个人中心
import request from "@/common/request.js";
export default {
  components: {
    index,
    cases,
    videozw,
    history,
    me,
  },
  data() {
    return {
      PageCur: "cases",
      // message: '2',
      openId: "",
      access_token: "",
      duration: 1,
    };
  },
  // 分享小程序
  onShareAppMessage(res) {
    return {
      title: "作文智判慧改小程序，让你的学习更简单！",
    };
  },
  onLoad() {},
  methods: {
    NavChange: function (e) {
      console.log(e.currentTarget.dataset.cur);

      this.PageCur = e.currentTarget.dataset.cur;
    },
  },
};
</script>

<style lang="scss">
.box {
  margin: 20upx 0;
}

.box view.cu-bar {
  margin-top: 20upx;
}

.logo_btn {
  width: 38 * 2rpx;
  height: 38 * 2rpx;
  position: absolute;
  z-index: 2;
  border-radius: 50%;
  top: -40rpx;
  left: 0rpx;
  right: 0;
  margin: auto;
  padding: 0;
}

.cu-bar.tabbar .action.add-action {
  padding-top: 56rpx !important;
}

.color_main {
  color: #07a069;
}
</style>
