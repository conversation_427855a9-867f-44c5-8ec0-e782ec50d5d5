<!-- 注册页面 -->
<template>
  <view class="t-login">
    <!-- 页面装饰图片 -->
    <image
      class="img-a"
      src="https://zhoukaiwen.com/img/loginImg/2.png"
    ></image>
    <image
      class="img-b"
      src="https://zhoukaiwen.com/img/loginImg/3.png"
    ></image>
    <!-- logo -->
    <image
      class="cw_logo"
      src="https://cdn.zhoukaiwen.com/cw_loginlogo_1.png"
      mode="widthFix"
    ></image>
    <!-- 标题 -->
    <view class="t-b">用户注册</view>
    <view class="t-b2">欢迎加入测文网</view>
    <form class="cl">
      <view class="t-a">
        <image src="https://cdn.zhoukaiwen.com/icon_zh.svg"></image>
        <view class="line"></view>
        <input
          placeholder="请输入账号"
          maxlength="50"
          v-model="form.userName"
        />
      </view>

      <view class="t-a">
        <image src="https://cdn.zhoukaiwen.com/icon_zh.svg"></image>
        <view class="line"></view>
        <input
          placeholder="请输入姓名"
          maxlength="50"
          v-model="form.nickName"
        />
      </view>

      <view class="t-a">
        <image src="https://zhoukaiwen.com/img/loginImg/yz.png"></image>
        <view class="line"></view>
        <input
          type="password"
          maxlength="20"
          placeholder="请输入密码"
          v-model="form.password"
        />
      </view>

      <view class="tips-text">注册成功后，可在"个人资料"中完善其他信息</view>

      <view class="agreement">
        <checkbox-group @change="checkboxChange" style="display: inline-block">
          <checkbox
            style="transform: scale(0.7, 0.7)"
            :checked="tongyi"
            value="tongyi"
          ></checkbox>
        </checkbox-group>

        <text>我已阅读并同意</text>
        <text class="text-blue" @tap="goYhxy">《用户服务协议》</text>
        <text>及</text>
        <text class="text-blue" @tap="goYsxy">《隐私政策》</text>
      </view>

      <button @tap="register()" :disabled="tongyi !== 1">
        {{ tongyi === 1 ? "注 册" : "请阅读并同意协议" }}
      </button>

      <view class="t-f" @tap="goLogin">已有账号？去登录</view>
    </form>
  </view>
</template>

<script>
import request from "@/common/request.js";
export default {
  data() {
    return {
      openId: "",
      tongyi: 0,

      form: {
        userName: "",
        nickName: "",
        password: "",
      },
    };
  },
  onLoad() {
    // 微信登录获取openId
    // uni.login({
    //   provider: "weixin",
    //   success: (loginRes) => {
    //     console.log(loginRes);
    //     this.wxLogin(loginRes.code);
    //   },
    // });
  },
  methods: {
    checkboxChange(e) {
      console.log(e.detail.value);
      if (e.detail.value.length < 1) {
        this.tongyi = 0;
      } else {
        this.tongyi = 1;
      }
    },
    goYhxy() {
      uni.navigateTo({
        url: "/pages/me/yhxy",
      });
    },
    goYsxy() {
      uni.navigateTo({
        url: "/pages/me/ysxy",
      });
    },
    goLogin() {
      uni.navigateTo({
        url: "/pages/user/login",
      });
    },
    wxLogin(code) {
      let opts = {
        url: "wx/getAccessToken",
        method: "POST",
      };
      request.httpRequest(opts, { code: code }).then((res) => {
        const data = JSON.parse(res.data);
        this.openId = data.openid;
      });
    },
    // 注册
    register() {
      // 表单验证
      if (!this.form.userName) {
        return uni.showToast({ title: "请输入账号", icon: "none" });
      }
      if (!this.form.nickName) {
        return uni.showToast({ title: "请输入姓名", icon: "none" });
      }
      if (!this.form.password) {
        return uni.showToast({ title: "请输入密码", icon: "none" });
      }

      // 提交注册
      const data = {
        openId: this.openId,
        ...this.form,
      };

      request
        .httpRequest(
          {
            url: "wx/register",
            method: "post",
          },
          data
        )
        .then((res) => {
          if (res.code === 200) {
            uni.showToast({
              title: "注册成功",
              duration: 1500,
            });
            setTimeout(function () {
              uni.navigateTo({
                url: "/pages/user/login",
              });
            }, 1200);
          } else {
            uni.showToast({
              title: res.message || "注册失败",
              icon: "none",
            });
          }
        })
        .catch((err) => {
          uni.showToast({
            title: "注册失败，请稍后重试",
            icon: "none",
          });
        });
    },
  },
};
</script>

<style>
.img-a {
  position: absolute;
  width: 100%;
  top: -150rpx;
  right: 0;
}
.img-b {
  position: absolute;
  width: 50%;
  bottom: 0;
  left: -50rpx;
}
.t-login {
  width: 650rpx;
  margin: 0 auto;
  font-size: 28rpx;
  color: #000;
  padding-bottom: 50rpx;
}
.cw_logo {
  width: 150rpx;
  margin: 150rpx 0 30rpx -20rpx;
}

.t-login button {
  font-size: 28rpx;
  background: #21b287;
  color: #fff;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 50rpx;
  box-shadow: 0 5px 7px 0 rgba(86, 119, 252, 0.2);
}

.t-login button[disabled] {
  background: #cccccc;
  color: #ffffff;
}

.t-login input {
  padding: 0 20rpx 0 120rpx;
  height: 90rpx;
  line-height: 90rpx;
  margin-bottom: 30rpx;
  background: #f8f7fc;
  border: 1px solid #e9e9e9;
  font-size: 28rpx;
  border-radius: 50rpx;
}

.t-login .t-a {
  position: relative;
}

.t-login .t-a image {
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  left: 40rpx;
  top: 28rpx;
  margin-right: 20rpx;
}
.t-login .t-a .line {
  width: 2rpx;
  height: 40rpx;
  background-color: #dedede;
  position: absolute;
  top: 28rpx;
  left: 98rpx;
}

.t-login .t-b {
  text-align: left;
  font-size: 46rpx;
  color: #000;
  font-weight: bold;
}
.t-login .t-b2 {
  text-align: left;
  font-size: 32rpx;
  color: #aaaaaa;
  padding: 10rpx 0 50rpx 0;
}

.t-login .t-f {
  text-align: center;
  color: #21b287;
  margin: 30rpx 0;
}

.agreement {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
  margin: 20rpx 0;
}

.text-blue {
  color: #21b287;
}

.tips-text {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  margin: 20rpx 0;
}
</style>
