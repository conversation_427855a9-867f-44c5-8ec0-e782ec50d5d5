<template>
	<view class="components-home" style="position: relative;">
		<view class="topBg" style="background-image:url(https://cdn.zhoukaiwen.com/cewen_me_bg.png);">
			<view class="cu-custom " >
				<view class="fixed text-center" :style="style" style="justify-content: center;">
					<view class="text-white text-bold text-lg" :style="[{top:StatusBar + 'px'}]">
						我的作文 
					</view>
				</view>
			</view>
		</view>
		
		<view class="mainBox" :style="[{top:CustomBar +10+ 'px',overflow: 'auto',paddingBottom: '200rpx'}]">
			<view class="padding bg-white" style="border-radius: 20rpx;">
				<view class="cu-form-group" style="padding: 0;">
					<view class="title text-bold text-black">
						<text class="margin-right-xs" style="color: red;">*</text>
						所在年级
					</view>
					<form-select placeholder="请选择所在年级" v-model="compositionInfo.grade" :dataList="Grade" :text="'text'" :name="'value'" />
				</view>
				
				<view class="cu-form-group" style="padding: 0;">
					<view class="title text-bold text-black">
						<text class="margin-right-xs" style="color: red;">*</text>
						作文文体
					</view>
					<form-select placeholder="请选择作文文体" v-model:value="compositionInfo.wenTi" :dataList="EssayCategories" />
				</view>
				
				<view class="cu-form-group" style="padding: 0;">
					<view class="title text-bold text-black">
						<text class="margin-right-xs" style="color: red;">*</text>
						作者姓名
					</view>
					<input class="text-right" v-model="compositionInfo.author" placeholder="请填写作者姓名" name="input"></input>
				</view>

				<view class="cu-form-group" style="padding: 0;">
					<view class="title text-bold text-black">
						<text class="margin-right-xs" style="color: red;">*</text>
						作文单元
					</view>
					<form-select placeholder="请选择作文单元" v-model:value="compositionInfo.did" :dataList="unitList" :text="'dname'" :name="'did'" />
				</view>
				<view class="cu-form-group" style="padding: 0;">
					<view class="title text-bold text-black">
						<text class="margin-right-xs" style="color: red;">*</text>
						作文满分值
					</view>
					<form-select placeholder="请选择作文满分值" v-model:value="compositionInfo.fullScore" :dataList="ScoreEnumOptions" />
				</view>
				<view class="cu-form-group" style="padding: 0;">
					<view class="title text-bold text-black">
						<text class="margin-right-xs" style="color: red;">*</text>
						字数要求
					</view>
					<text class="text-right" style="color: #555;">
						本篇写作不少于 {{ compositionInfo.requireWords }} 字
					</text>
				</view>
				<view class="cu-form-group" style="padding: 0;">
					<view class="title text-bold text-black">
						写作要求
					</view>
					<input class="text-right" v-model="compositionInfo.compositionRequireTitle" placeholder="请填写测评要求" name="input"></input>
				</view>
			</view>
			
			<view class="padding bg-white" style="border-radius: 20rpx;margin-top: 20rpx;">
				<view class="cu-form-group" style="padding: 0;">
					<view class="title text-bold text-black">
						<text class="margin-right-xs" style="color: red;">*</text>
						作文标题
					</view>
					<input class="text-right" v-model="compositionInfo.title" placeholder="请填写作文标题" name="input"></input>
				</view>
				
				<view class="cu-bar bg-white" style="border-top: 1rpx solid #eee;">
					<view class=" text-bold text-black">
						<text class="margin-right-xs" style="color: red;">*</text>
						图片上传
					</view>
					<view class="margin-right-sm">
						{{compositionInfo.images.length}}/4
					</view>
				</view>
				<view class="cu-form-group">
					<view class="grid col-4 grid-square flex-sub">
						<view class="bg-img" v-for="(item,index) in compositionInfo.images" :key="index" @tap="ViewImage"
							:data-url="item">
							<image :src="`${webUrl}/${item}`" mode="aspectFill"></image>
							<view class="cu-tag bg-red" @tap.stop="DelImg" :data-index="index">
								<text class='cuIcon-close'></text>
							</view>
						</view>
						<view class="solids" @tap="ChooseImage" v-if="compositionInfo.images.length<4">
							<text class='cuIcon-cameraadd'></text>
						</view>
					</view>
				</view>
				<view class="padding-lr padding-bottom-sm bg-white text-smdf text-gray">温馨提醒：请按文章顺序上传图片</view>
			</view>
			
			<view class="padding-tb flex justify-between margin-top-xs">
				<button @click="viewDrawToCanvas(0)" class="cu-btn bg-green3 lg" style="width: 48%;">预览文章</button>
				<button v-if="userInfo.evaluationNumber >= 1" @click="viewDrawToCanvas(2)"
					class="cu-btn bg-green3 lg" style="width: 48%;">文章测评</button>
				<button v-else class="cu-btn bg-green3 lg" style="width: 48%;" @click="goBuy()">请先获得次数</button>
			</view>
		</view>
		
		
		
		
		<canvas class='canvas-poster' canvas-id='canvasposter' :style="{'height':canvasHeight+'px'}"></canvas>

		<view style="height: 150rpx;width: 1rpx;"></view>
	</view>
</template>

<script>
	import request,{webUrl} from "@/common/request.js";
	import formSelect from "@/components/form-select.vue";
	import {
		EssayCategories,
		Grade,
		ScoreGradeMap,
		ScoreEnumOptions,
		WordCountMap
	} from "@/common/constants.js";
	export default {
		components: {
			formSelect
		},
		data() {
			return {
				webUrl,
				CustomBar: this.CustomBar,
				EssayCategories,
				Grade,
				ScoreGradeMap,
				ScoreEnumOptions,
				WordCountMap,
				// 作文信息
				compositionInfo: {
					title: "",
					author: "",
					grade: "",
					wenTi: "",
					fullScore:'',
					requireWords:'',
					compositionRequireTitle:'',
					did:'0',
					images: [],
				},
				unitList:[],
				// 用户信息
				userInfo: null,
				// canvas相关
				canvasHeight: 0,
			};
		},
		computed: {
			style() {
				const StatusBar= this.StatusBar;
				const CustomBar= this.CustomBar;
				const bgImage = this.bgImage;
				const style = `height:${CustomBar}px;padding-top:${StatusBar}px;`;
				if (this.bgImage) {
					style = `${style}background-image:url(${bgImage});`;
				}
				return style
			}
		},
		mounted() {
			this.getUserInfo();
		},
		methods: {
			// 获取用户信息
			getUserInfo() {
				const userInfo = uni.getStorageSync("userInfo");
				if (userInfo) {
					this.getUser()
				} else {
					uni.reLaunch({
						url: "/pages/user/login",
					});
				}
			},
			getUser() {
				let opts = {
					url: "auth/user_info",
					method: "GET",
				};
				request.httpTokenRequest(opts).then((res) => {
					if (res.data.id) {
						this.userInfo = res.data;
						this.compositionInfo.author = res.data.realName;
						const compositionInfo = uni.getStorageSync("composition-evaluation");
						if(Number(this.userInfo.userType) === 3){
							this.compositionInfo.grade = this.userInfo.clazzName ? this.userInfo.clazzName.split(',')[0].split('_')[0] : null;
							this.compositionInfo.fullScore = ScoreGradeMap[this.compositionInfo.grade];
							if (this.compositionInfo.grade && ['高一', '高二', '高三', '大学后'].includes(this.compositionInfo.grade)) {
								this.compositionInfo.wenTi = '议论文';
							} else {
								this.compositionInfo.wenTi = '叙事记叙文';
							}
						}
						if (compositionInfo) {
							this.compositionInfo = compositionInfo;
							this.getUnitList(this.compositionInfo.grade);
						}else if(this.userInfo?.gradeName||this.compositionInfo.grade){
							this.getUnitList(this.userInfo.gradeName||this.compositionInfo.grade);
							this.compositionInfo.grade = this.userInfo.gradeName||this.compositionInfo.grade;
							this.compositionInfo.fullScore = ScoreGradeMap[this.compositionInfo.grade];
							this.compositionInfo.requireWords = WordCountMap[this.compositionInfo.grade];
							if (this.compositionInfo.grade && ['高一', '高二', '高三', '大学后'].includes(this.compositionInfo.grade)) {
								this.compositionInfo.wenTi = '议论文';
							} else {
								this.compositionInfo.wenTi = '叙事记叙文';
  						}
						}
					}
				});
			},
			getUnitList(grade){
				let opts = {
					url: "bse_danyuan/list",
					method: "POST",
				};
				request.httpTokenRequest(opts,{dgrade:grade}).then((res) => {
					const data=res.data||[];
					this.unitList = data.map(item=>{
						return {
							did:item.id,
							dname:`${item.dgrade}${item.ddanyuan}${item.dname}`
						}
					});
					this.unitList.unshift({
						did: '0',
						dname: '非单元作文'
					});
				})
			},

			// 图片处理相关方法
			async handleImageUpload(tempFilePaths) {
				for (let path of tempFilePaths) {
					try {
						const result = await this.uploadFile(path);
						const res = JSON.parse(result);
						if (res.code === 200) {
							if (this.compositionInfo.images.length) {
								this.compositionInfo.images.push(res.data);
							} else {
								this.compositionInfo.images = [res.data];
							}
						}
					} catch (error) {
						console.error("上传失败:", error);
						uni.showToast({
							title: "图片上传失败",
							icon: "none",
						});
					}
				}
			},

			ChooseImage() {
				uni.chooseImage({
					count: 4,
					sizeType: ["original", "compressed"],
					sourceType: ["album", "camera"],
					success: (res) => {
						this.handleImageUpload(res.tempFilePaths);
					},
				});
			},

			uploadFile(filePath) {
				const token = uni.getStorageSync("token");
				return new Promise((resolve, reject) => {
					uni.uploadFile({
						url: `${request.baseUrl}mon_file/upload-file`,
						filePath,
						name: "file",
						header: {
							Authorization: `Bearer ${token}`,
						},
						success: (res) => {
							resolve(res.data);
						},
						fail: reject,
					});
				});
			},

			// 图片预览和删除
			ViewImage(e) {
				uni.previewImage({
					urls: this.compositionInfo.images,
					current: e.currentTarget.dataset.url,
				});
			},

			DelImg(e) {
				uni.showModal({
					title: "删除照片",
					content: "确定要删除图片吗？",
					success: (res) => {
						if (res.confirm) {
							this.compositionInfo.images.splice(
								e.currentTarget.dataset.index,
								1
							);
						}
					},
				});
			},

			// 表单验证
			validateForm(checkTheme = true) {
				console.log("this.compositionInfo", this.compositionInfo);
				if (!this.compositionInfo.grade) {
					uni.showToast({
						title: "请选择年级",
						icon: "none"
					});
					return false;
				}
				if (!this.compositionInfo.wenTi) {
					uni.showToast({
						title: "请选择文体类型",
						icon: "none"
					});
					return false;
				}
				if (!this.compositionInfo.title) {
					uni.showToast({
						title: "请填写文章标题",
						icon: "none"
					});
					return false;
				}
				if (!this.compositionInfo.author) {
					uni.showToast({
						title: "请填写作者姓名",
						icon: "none"
					});
					return false;
				}
				if (!this.compositionInfo.images.length) {
					uni.showToast({
						title: "请先上传文章图片",
						icon: "none"
					});
					return false;
				}
				return true;
			},

			// Canvas绘制
			async viewDrawToCanvas(btnType) {
				if (!this.validateForm()) return;

				uni.showLoading({
					title: "请稍等，图片合成中..."
				});

				try {
					const {
						tempFilePath
					} = await this.drawCanvasAndSave();

					if (btnType === 0) {
						uni.previewImage({
							urls: [tempFilePath],
							current: 0,
						});
					} else {
						this.navigateToEdit(tempFilePath);
					}
				} catch (error) {
					console.error("Canvas处理失败:", error);
					uni.showToast({
						title: "图片处理失败",
						icon: "none",
					});
				} finally {
					uni.hideLoading();
				}
			},

			async drawCanvasAndSave() {
				const ctx = uni.createCanvasContext("canvasposter", this);
				let newHeight = 0;

				ctx.clearRect(0, 0, 0, 0);
				ctx.draw();

				for (let i = 0; i < this.compositionInfo.images.length; i++) {
					await new Promise((resolve) => {
						uni.getImageInfo({
							src: `${webUrl}/${this.compositionInfo.images[i]}`,
							complete: (image) => {
								const width = 350;
								const height = Math.ceil(image.height * (width / image.width));
								newHeight += height;
								ctx.drawImage(image.path, 0, newHeight - height, width, height);
								ctx.draw(true);
								this.canvasHeight = newHeight;
								resolve();
							},
						});
					});
				}

				return new Promise((resolve) => {
					setTimeout(() => {
						uni.canvasToTempFilePath({
								canvasId: "canvasposter",
								success: resolve,
							},
							this
						);
					}, 600);
				});
			},
			// 测评次数不足-跳转购买页
			goBuy(){
				uni.navigateTo({
					url: '/pages/me/APay'
				})
			},
			// 测评次数够，扣除次数1次 // 宇杰检查：检查接口，先调用此接口扣除，成功在进行批阅
			subNum(){
				return new Promise((resolve, reject) => {
					let opts = {
						url: "sys_user/updateTimes",
						method: "POST",
					};
					let params = {
						"type": 'sub',
						"rowType": 'evaluationNumber',	
						"numbers": 1,	
						remark: `测评作文《${this.compositionInfo.title}》消耗1次测评次数`
					};
					request.httpTokenRequest(opts,params).then((res) => {
						if(res.data<0){
							uni.showToast({
								title: "次数不足，请先充值！",
								duration: 1300,
								icon: "none"
							});
							this.userInfo.evaluationNumber = res.data;
        			uni.setStorageSync("userInfo", this.userInfo);
							return reject(true);
						}
						return resolve(false);
					})
				})
			},
			async navigateToEdit(tempFilePath) {
				const res = await this.subNum();
				if(res) return;
				uni.setStorageSync("composition-evaluation", this.compositionInfo);
				const params = {
					imgUrl: tempFilePath,
					compositionPath: this.compositionInfo.images.join(","),
					title: this.compositionInfo.title,
					author: this.compositionInfo.author,
					grade: this.compositionInfo.grade,
					wenTi: this.compositionInfo.wenTi,
					fullScore: this.compositionInfo.fullScore,
					did: this.compositionInfo.did,
					requireWords: this.compositionInfo.requireWords,
					compositionRequireTitle: this.compositionInfo.compositionRequireTitle,
					evaluateSources: 0
				};
				const url = `/pages/job/recognition-score?${Object.entries(params)
        .map(([key, value]) => `${key}=${value}`)
        .join("&")}`;
				uni.navigateTo({
					url
				});
			},
		},
	};
</script>

<style lang="scss" scoped>
	.topBg{
		background-size: 100% 100%;
		/* background-size: cover; */
		height: 760rpx;
		width: 750rpx;
		overflow: hidden;
		flex-direction: column;
		padding: 20rpx;
		position: absolute;
		top: 0;
		left: 0;
	}
	.mainBox{
		width: 700rpx;
		position: absolute;
		left: 25rpx;
	}
	/* 绘制图片canvas样式 */
	.canvas-poster {
		position: fixed;
		width: 350px;
		top: 100%;
		left: 100%;
	}

	.form-group {
		.form-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 24rpx 0;
			border-bottom: 1rpx solid #eee;

			&:last-child {
				border-bottom: none;
			}

			.label {
				display: flex;
				align-items: center;
				font-size: 30rpx;
				color: #333;
				font-weight: bold;

				text {
					&:first-child {
						font-size: 32rpx;
						margin-right: 12rpx;
					}
				}
			}

			.picker-value {
				font-size: 28rpx;
				color: #555;
				padding: 0 20rpx;
			}

			.input-value {
				font-size: 28rpx;
				color: #555;
				text-align: right;
				padding: 0 20rpx;
			}
		}
	}

	.base-info {
		.title-section {
			margin-bottom: 30rpx;

			.main-title {
				font-size: 40rpx;
				font-weight: bold;
				color: #333;
				line-height: 1.4;
			}

			.divider {
				width: 120rpx;
				height: 6rpx;
				background: linear-gradient(90deg, #2979ff, #5cadff);
				border-radius: 3rpx;
				margin: 20rpx 0;
			}
		}

		.tag-group {
			display: flex;
			align-items: center;
			margin: 20rpx 0;

			.tag-item {
				display: flex;
				align-items: center;
				margin-right: 20rpx;
				background: #f8f8f8;
				padding: 6rpx 16rpx;
				border-radius: 6rpx;

				.cuIcon-title,
				.cuIcon-write,
				.cuIcon-rank {
					font-size: 28rpx;
				}

				text {
					&:first-child {
						margin-right: 6rpx;
					}

					&:last-child {
						font-size: 26rpx;
						color: #666;
					}
				}
			}
		}

		.select-btn {
			display: flex;
			align-items: center;
			justify-content: center;
			margin-top: 30rpx;
			padding: 20rpx 0;
			background: #f8f8f8;
			border-radius: 8rpx;
			color: #2979ff;
			font-size: 28rpx;

			.cuIcon-edit {
				margin-right: 8rpx;
			}

			&:active {
				opacity: 0.8;
			}
		}

		.time-info {
			margin-top: 30rpx;

			.time-value {
				display: flex;
				align-items: center;
				color: #666;
				font-size: 28rpx;
				letter-spacing: 2rpx;

				.cuIcon-time {
					color: #2979ff;
					font-size: 36rpx;
					margin-right: 16rpx;
				}

				text {
					&:not(.separator) {
						margin: 0 8rpx;
						color: #333;
						font-weight: 500;
					}

					&.separator {
						margin: 0 20rpx;
						color: #999;
						font-weight: normal;
					}
				}
			}
		}
	}
</style>