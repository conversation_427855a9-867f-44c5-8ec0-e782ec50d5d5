<!-- 我的积分 -->
<template>
	<view class="container">
		<cu-custom bgColor="bg-gradual-blue" :isBack="true">
			<block slot="backText"></block>
			<block slot="content">我的积分</block>
		</cu-custom>
		
		<view class="cu-bar bg-white">
			<view class="action">
				<text class="cuIcon-title text-blue"></text>
				<text class="text-df">积分：{{score}}</text>
			</view>
			<view @click="goPay()" class="action text-blue">
				<text class="text-grey text-smdf">次数：{{num_dh}}</text>
				<text class="text-df margin-left-sm text-bold">获取积分</text>
			</view>
		</view>
		
		<view class="cu-list menu sm-border solid-top">
			<view class="cu-item" v-for="(item,index) in jfList" :key="index">
				<view class="content padding-tb-sm">
					<view>
						<text :class="item.payOrBuy =='pay'? 'text-orange':'text-blue'" class="cuIcon-rechargefill  margin-right-xs"></text> 
						<text class="text-bold">积分 {{item.payOrBuy =='pay'? '-':'+'}} {{item.numJf}}</text>
					</view>
					<view class="text-gray text-sm">
						<text class="cuIcon-infofill margin-right-xs"></text> {{item.notes}}</view>
				</view>
				<view class="action">
					<text class="text-grey text-smdf">{{item.buyTime | timeFt}}</text>
				</view>
			</view>
		</view>
		<view class="page-box" v-if="jfList.length <= 0">
			<view>
				<view class="centre">
					<image src="https://cdn.zhoukaiwen.com/noData1.png" mode="widthFix"></image>
					<view class="explain">
						暂无提交记录
						<view class="tips">可以先去参与竞赛活动</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import request from '@/common/request.js';
	import moment from "@/components/moment/index.js"; // 格式化时间 插件
	export default {
		data() {
			return {
				loginType: 0,
				score: 0,
				jfList: [],
				num_dh: 0
			}
		},
		onLoad(option) {
			this.getUserInfo();
		},
		methods: {
			getUserInfo(){
				var that = this;
				uni.getStorage({
					key: 'userInfo',
					success: function(res) {
						console.log(res.data);
						that.loginType = 1;
						that.getUserDetail(res.data.openId);
						that.getjfList(res.data.openId)
						
					},fail:function(err) {
						that.loginType = 0;
					}
				});
			},
			getUserDetail(openId){
				let opts = {
					url: 'api/user/info',
					method: 'GET'
				};
				let params = {
					"openId": openId
				};
				console.log(params)
				request.httpRequest(opts, params).then(res => {
					console.log(res.data.data);
					this.score = res.data.data.score;
					this.num_dh = res.data.data.num_dh;
					
				});
			},
			getjfList(openId){
				let opts = {
					url: 'api/buy/jfList',
					method: 'GET'
				};
				let params = {
					"openId": openId
				};
				console.log(params)
				request.httpRequest(opts, params).then(res => {
					console.log(res.data.data);
					this.jfList = res.data.data;
				});
			},
			goPay(){
				uni.navigateTo({
					url: '/pages/main/buy'
				})
			}
		},
		filters: {
			timeFt:function(time){
				// let time = "1709130283826"
				let mm = Number(time)
				let ss = moment(mm).format('YYYY.MM.DD HH:mm');
				return ss;
			}
		}
	}
</script>

<style lang="scss" scoped>
	.centre {
		text-align: center;
		margin: 10rpx auto 50rpx;
		font-size: 32rpx;
		image {
			width: 300rpx;
			border-radius: 50%;
			margin: 0 auto;
		}
		.tips {
			font-size: 24rpx;
			color: #999999;
			margin-top: 20rpx;
		}
	}
</style>
