<template>
  <view class="container">
    <cu-custom bgColor="bg-gradual-blue" :isBack="true">
      <block slot="backText"></block>
      <block slot="content">作文详情</block>
    </cu-custom>

    <view class="cu-list menu sm-border bg-white">
      <view class="cu-item" v-if="compositionInfo.compositionTitle">
        <view class="content padding-tb-sm">
          <view>
            <!-- <text class="cuIcon-peoplefill text-blue margin-right-xs"></text> -->
            <text>{{ compositionInfo.compositionTitle }}</text>
          </view>
          <view class="text-gray text-sm">
            <text>{{ compositionInfo.compositionWenTi }}</text>
          </view>
        </view>
        <!-- <view class="action text-red text-dflg text-bold">
          <text>{{ compositionInfo.evaluateScore }}</text>
        </view> -->
      </view>

      <view class="padding-lr padding-tb-sm">
        <view class="text-lg text-black text-bold text-center">{{
          compositionInfo.title
        }}</view>
        <view
          class="margin-bottom-sm text-gray margin-top-xs flex justify-between"
        >
          <text>作者：{{ compositionInfo.answerName }}</text>
          <text>{{ compositionInfo.submitTime }}</text>
        </view>
        <view style="width: 100%">
          <rich-text
            :nodes="compositionInfo.content"
            style="white-space: pre-wrap"
          ></rich-text>
        </view>
      </view>

      <!-- 评分区域 -->
      <view style="background-color: #ffffff; padding: 0rpx 30rpx 15rpx 0rpx">
        <view class="cu-list menu sm-bordercard-menu">
          <view class="cu-item" style="min-height: 86rpx">
            <view class="content">
              <image src="../../static/logo.jpg" class="png" mode="aspectFit" />
              <text class="text-grey">作文智判慧改评分</text>
            </view>
            <!-- <view class="action">
              <view @click="handleEvaluate" class="cu-tag round bg-blue light">
                批阅/评分
              </view>
            </view> -->
          </view>
        </view>

        <!-- 评分标签页 -->
        <scroll-view scroll-x class="bg-white nav solids-top">
          <view class="flex text-center">
            <view
              v-for="(item, index) in evaluationTabs"
              :key="index"
              class="cu-item flex-sub"
              :class="index == currentTab ? 'text-blue cur' : ''"
              @tap="switchTab(index)"
            >
              {{ item }}
            </view>
          </view>
        </scroll-view>

        <!-- 无评分数据展示 -->
        <view v-if="!compositionInfo.evaluateContent.score" class="page-box">
          <view class="centre">
            <image
              src="https://cdn.zhoukaiwen.com/noData1.png"
              mode="widthFix"
            />
            <view class="explain">
              暂未评分
              <view class="tips">修改文章后可点击【批阅/评分】</view>
            </view>
          </view>
        </view>

        <!-- 评分数据展示 -->
        <template v-else>
          <!-- 评分展示 -->
          <view v-if="currentTab === 0" class="bg-white padding">
            <view class="text-red text-content">
              <text class="text-xxl text-bold"
                >评分：{{ compositionInfo.evaluateContent.score }}</text
              >
            </view>
            <view class="text-red text-content">
              <text class="text-xl text-bold">{{
                compositionInfo.evaluateContent.Classified
              }}</text>
            </view>

            <!-- 评分详情 -->
            <view
              v-for="(item, index) in compositionInfo.evaluateContent.CTList"
              :key="index"
              class="flex margin-top"
            >
              <text class="margin-right" style="width: 160rpx">{{
                item.Ctitle
              }}</text>
              <view class="cu-progress round striped">
                <view
                  :class="{
                    'bg-red': item.Cpercentage < 60,
                    'bg-yellow': item.Cpercentage < 80,
                    'bg-blue': item.Cpercentage >= 80,
                  }"
                  :style="{ width: item.Cpercentage + '%' }"
                >
                  {{ item.Cpercentage + "分" }}
                </view>
              </view>
            </view>

            <!-- 参考意见 -->
            <view class="text-grey text-df margin-top-lg">
              参考意见：
              <rich-text
                :nodes="compositionInfo.evaluateContent.Advice"
                style="white-space: pre-wrap"
              />
            </view>
          </view>

          <!-- 评语展示 -->
          <view v-if="currentTab === 1" class="bg-white padding">
            <view class="text-content">
              <text class="text-black text-xl text-bold">评语：</text>
              <rich-text
                class="text-grey"
                :nodes="compositionInfo.evaluateContent.Comments"
                style="white-space: pre-wrap"
              />
            </view>
          </view>

          <!-- 修改建议 -->
          <view
            v-if="currentTab === 2"
            class="bg-white padding-lr padding-bottom"
          >
            <view class="text-df">
              <view class="text-black text-bold">注意：</view>
              <view
                v-for="(tip, index) in correctionTips"
                :key="index"
                :style="{ color: tip.color }"
                class="text-red text-bold margin-left-lg margin-top-xs"
              >
                {{ tip.text }}
              </view>
            </view>
            <view class="text-grey text-content" style="white-space: pre-wrap">
              <rich-text
                :nodes="compositionInfo.evaluateContent.Content"
                style="white-space: pre-wrap"
              />
            </view>
          </view>
        </template>
      </view>
    </view>
  </view>
</template>

<script>
import request from "@/common/request.js";
import moment from "@/components/moment/index.js"; // 格式化时间 插件
export default {
  data() {
    return {
      compositionInfo: {},
      currentTab: 0,
      evaluationTabs: ["评分", "评语", "修改建议"],
      // 修改建议提示
      correctionTips: [
        { color: "#ff6141", text: "正文中红色的字：段落评语。" },
        {
          color: "#3cb371",
          text: "正文中绿色的字：表示文字表达可能有不妥之处。",
        },
        { color: "#0047b6", text: "正文中蓝色的字：错误标点突出显示。" },
        { color: "#9932cc", text: "正文中紫色的字：针对错误的修改建议。" },
        {
          color: "#ff8e35",
          text: "正文中橙色的字：表示文字表达可能有歧义的字",
        },
      ],
    };
  },
  onLoad(option) {
    this.getDetail(option.id);
  },
  methods: {
    // UI相关方法
    switchTab(index) {
      this.currentTab = index;
    },
    getDetail(id) {
      request
        .httpTokenRequest({
          url: `biz_composition_log/${id}`,
          method: "GET",
        })
        .then((res) => {
          this.compositionInfo = res.data;
          this.compositionInfo.submitTime = moment(
            this.compositionInfo.submitTime
          ).format("YYYY.MM.DD HH:mm");
          this.compositionInfo.evaluateContent = JSON.parse(
            res.data.evaluateContent
          );
          console.log("打印日志:=>", this.compositionInfo.evaluateContent);
        });
    },
    goEdit() {
      uni.navigateTo({
        url: `/pages/main/edit?state=1&imgUrl=1&id=${this.compositionInfo.id}`,
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
