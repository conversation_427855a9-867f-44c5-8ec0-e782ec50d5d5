<template>
  <view class="container">
    <!-- 自定义顶部 -->
    <cu-custom bgColor="bg-gradual-blue" :isBack="true">
      <block slot="backText">返回</block>
      <block slot="content">视频详情</block>
    </cu-custom>

    <!-- 视频播放器区域 -->
    <view class="video-container">
      <video
        id="myVideo"
        :src="videoUrl"
        :poster="videoDetails.Vimg || ''"
        controls
        show-center-play-btn
        enable-play-gesture
        object-fit="contain"
        @error="videoErrorCallback"
        @fullscreenchange="fullscreenChange"
        class="main-video"
      ></video>
    </view>

    <!-- 视频信息卡片 -->
    <view class="info-card">
      <view class="video-title">
        <text>{{ videoDetails.Title || "视频标题" }}</text>
      </view>

      <view class="video-tags">
        <view class="cu-tag bg-blue light radius" v-if="videoDetails.Grade">
          <text class="cuIcon-people"></text> {{ videoDetails.Grade }}
        </view>
        <view class="cu-tag bg-green light radius" v-if="videoDetails.Wenti">
          <text class="cuIcon-tagfill"></text> {{ videoDetails.Wenti }}
        </view>
        <view
          class="cu-tag bg-purple light radius"
          v-if="videoDetails.Scorepoints"
        >
          <text class="cuIcon-medal"></text> {{ videoDetails.Scorepoints }}
        </view>
      </view>

      <!-- <view class="divider"></view> -->

      <!-- 操作按钮 -->
      <!-- <view class="action-bar">
        <button class="cu-btn icon bg-red shadow-blur" @tap="playVideo">
          <text class="cuIcon-video"></text> 播放
        </button>
        <button class="cu-btn icon bg-blue shadow-blur">
          <text class="cuIcon-share"></text> 分享
        </button>
        <button class="cu-btn icon bg-green shadow-blur" @tap="downloadVideo">
          <text class="cuIcon-download"></text> 下载
        </button>
        <button class="cu-btn icon bg-orange shadow-blur" @tap="toggleFavorite">
          <text class="cuIcon-favor" :class="{ 'text-red': isFavorite }"></text>
          收藏
        </button>
      </view> -->
    </view>

    <!-- 视频介绍 -->
    <view class="content-card">
      <view class="card-title">
        <text class="cuIcon-titles text-blue"></text> 视频介绍
      </view>

      <view class="card-content">
        <text class="description">
          本视频《{{ videoDetails.Title || "视频标题" }}》专为{{
            videoDetails.Grade || "学生"
          }}设计，提供优质的{{ videoDetails.Wenti || "写作" }}指导，重点关注{{
            videoDetails.Scorepoints || "写作技巧"
          }}。
          通过观看本视频，学生将学习如何更有效地开展写作，提升自己的表达能力和文章质量。
        </text>
      </view>
    </view>

    <!-- 相关推荐 -->
    <!-- <view class="content-card">
      <view class="card-title">
        <text class="cuIcon-titles text-blue"></text> 相关推荐
      </view>

      <view class="related-videos" v-if="relatedVideos.length > 0">
        <view
          class="related-item"
          v-for="(item, index) in relatedVideos"
          :key="index"
          @tap="navigateToVideo(item)"
        >
          <image
            :src="
              item.Vimg ||
              'https://api.cewenwang.com/Upload/img/zuowenben_kecheng/001.png'
            "
            mode="aspectFill"
          ></image>
          <view class="related-info">
            <view class="related-title text-cut">{{ item.Title }}</view>
            <view class="cu-tag bg-blue light sm radius" v-if="item.Grade">{{
              item.Grade
            }}</view>
            <view class="cu-tag bg-green light sm radius" v-else>推荐课程</view>
          </view>
        </view>
      </view>
      <view v-else class="no-related">
        <text class="text-gray">暂无相关推荐</text>
      </view>
    </view> -->

    <!-- 加载中 -->
    <view class="loading-container" v-if="loading">
      <view class="cu-load loading"></view>
      <text>加载中...</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      videoUrl: "",
      videoContext: null,
      videoDetails: {
        Id: 0,
        Title: "",
        Grade: "",
        Wenti: "",
        Scorepoints: "",
        Vurl: "",
        Vimg: "",
        IsShopping: false,
      },
      relatedVideos: [],
      loading: false,
      isFavorite: false,
    };
  },
  onLoad(options) {
    // 初始化加载状态
    this.loading = true;

    // 从URL参数获取视频地址和标题
    if (options.videoUrl) {
      this.videoUrl = decodeURIComponent(options.videoUrl);
    }

    if (options.title) {
      this.videoDetails.Title = decodeURIComponent(options.title);
    }

    // 如果有传入完整视频详情对象ID，则获取详情
    if (options.id) {
      this.getVideoDetails(options.id);
    } else {
      // 测试数据
      this.setDefaultData();
      // 获取相关视频
      this.getRelatedVideos();
    }

    // 初始化视频上下文
    this.videoContext = uni.createVideoContext("myVideo", this);
  },
  onReady() {
    // 视频元素初始化完成
  },
  methods: {
    // 获取视频详情 - 更新为与videozw.vue相同的请求风格
    getVideoDetails(id) {
      // 显示加载中
      uni.showLoading({
        title: "加载中",
      });

      this.loading = true;

      // 使用与videozw.vue一致的请求格式
      uni.request({
        url: `https://api.cewenwang.com/api/Video/${id}`,
        method: "GET",
        header: {
          "Content-Type": "application/json",
        },
        success: (res) => {
          if (res.data) {
            this.videoDetails = res.data;
            this.videoUrl = res.data.Vurl;

            // 获取相关视频
            this.getRelatedVideos();
          }
          this.loading = false;
          uni.hideLoading();
        },
        fail: () => {
          this.loading = false;
          uni.hideLoading();
          uni.showToast({
            title: "获取视频信息失败",
            icon: "none",
          });
        },
      });
    },

    // 获取相关视频
    getRelatedVideos() {
      uni.request({
        url: "https://api.cewenwang.com/api/VideoList/38", // 用相同的请求URL获取视频列表
        method: "GET",
        header: {
          "Content-Type": "application/json",
        },
        success: (res) => {
          // 过滤当前视频，最多取3个相关视频
          this.relatedVideos = (res.data || [])
            .filter((item) => item.Id !== this.videoDetails.Id)
            .slice(0, 3);
          this.loading = false;
          uni.hideLoading();
        },
        fail: () => {
          this.loading = false;
          uni.hideLoading();
          uni.showToast({
            title: "加载相关视频失败",
            icon: "none",
          });
        },
      });
    },

    // 设置默认数据（用于测试）
    setDefaultData() {
      this.videoDetails = {
        Id: 194,
        Zid: 0,
        Uid: 0,
        Title: "第一讲《如何开头——阳鸟凤头吐清音》",
        Grade: "初中段",
        Wenti: "记叙文",
        Scorepoints: "开篇押题",
        Vurl: "https://api.cewenwang.com/Upload/zuowenben_kecheng/001.mp4",
        Vimg: "https://api.cewenwang.com/Upload/img/zuowenben_kecheng/001.png",
        IsShopping: false,
      };

      if (!this.videoUrl) {
        this.videoUrl = this.videoDetails.Vurl;
      }

      this.loading = false;
    },

    // 跳转到相关视频
    navigateToVideo(video) {
      uni.navigateTo({
        url: `/pages/home/<USER>
      });
    },

    // 播放视频
    playVideo() {
      if (this.videoContext) {
        this.videoContext.play();
      }
    },

    // 收藏切换
    toggleFavorite() {
      this.isFavorite = !this.isFavorite;
      uni.showToast({
        title: this.isFavorite ? "已加入收藏" : "已取消收藏",
        icon: "none",
      });
    },

    // 下载视频
    downloadVideo() {
      uni.showModal({
        title: "提示",
        content: "是否下载此视频到本地？",
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: "开始下载，请在下载管理中查看进度",
              icon: "none",
              duration: 2000,
            });

            // 这里可以添加实际的下载逻辑
          }
        },
      });
    },

    // 视频播放错误回调
    videoErrorCallback(e) {
      console.log("视频错误信息:", e);
      uni.showToast({
        title: "视频加载失败",
        icon: "none",
      });
    },

    // 全屏状态变化
    fullscreenChange(e) {
      console.log("全屏状态:", e.detail.fullScreen);
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  background-color: #f8f8f8;
  min-height: 100vh;
}

.video-container {
  width: 100%;
  height: 422rpx;
  background-color: #000;
  position: relative;
}

.main-video {
  width: 100%;
  height: 100%;
}

.info-card {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
}

.video-title {
  font-size: 34rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.video-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 30rpx;
}

.video-tags .cu-tag {
  margin-right: 15rpx;
  margin-bottom: 10rpx;
  padding: 6rpx 20rpx;
}

.divider {
  height: 1rpx;
  background-color: #f0f0f0;
  margin: 20rpx 0;
}

.action-bar {
  display: flex;
  justify-content: space-between;
}

.action-bar .cu-btn {
  padding: 0 30rpx;
  height: 70rpx;
  font-size: 26rpx;
}

.content-card {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
}

.card-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.card-title text:first-child {
  margin-right: 10rpx;
}

.description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.7;
}

.related-videos {
  display: flex;
  flex-direction: column;
}

.related-item {
  display: flex;
  margin-bottom: 30rpx;
}

.related-item image {
  width: 180rpx;
  height: 120rpx;
  border-radius: 8rpx;
}

.related-info {
  margin-left: 20rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.related-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.text-cut {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.no-related {
  text-align: center;
  padding: 40rpx 0;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-container text {
  margin-top: 20rpx;
  color: #999;
  font-size: 28rpx;
}
</style>
