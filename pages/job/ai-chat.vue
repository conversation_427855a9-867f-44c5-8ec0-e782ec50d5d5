<template>
  <view class="container">
    <cu-custom bgColor="bg-gradual-blue" :isBack="true">
      <block slot="content">智能AI小助手</block>
    </cu-custom>
    <!-- 聊天记录 -->
    <scroll-view
      class="chat-list"
      scroll-y="true"
      :scroll-into-view="'msg-' + (messages.length - 1)"
    >
      <view
        v-for="(message, index) in messages"
        :key="index"
        :id="'msg-' + index"
        :class="['message', message.sender]"
      >
        <!-- ai头像 -->
        <image
          v-if="message.sender != 'user'"
          class="avatar"
          :src="message.aiAvatar"
          mode="aspectFill"
        />
        <!-- 消息内容 -->
        <view class="message-content">
          <!-- AI 回复时显示逐字效果 -->
          <view v-if="message.sender === 'ai' && message.isTyping">
            {{ message.displayText }}
            <text class="cursor">|</text>
            <!-- 光标效果 -->
          </view>
          <view v-else>
            {{ message.content }}
          </view>
        </view>
        <!-- 用户头像 -->
        <image
          v-if="message.sender == 'user'"
          class="avatar"
          :src="userAvatar"
          mode="aspectFill"
        />
      </view>
    </scroll-view>

    <!-- 输入框 -->
    <!-- <view class="box bg-white">
			<view class="cu-bar input">
				<view @tap="showModal" data-target="bottomModal" class="cu-avatar round margin-right-xs" style="background-image:url(https://ossweb-img.qq.com/images/lol/web201310/skin/big91012.jpg);"></view>
				<input @confirm="sendMessage" v-model="inputMessage" placeholder="请输入消息" :adjust-position="false" class="solid-bottom" maxlength="300" cursor-spacing="10" style="margin: 0 10rpx 0 10rpx;"></input>
				<view class="action">
					<view @tap="showModal" data-target="Modal" class="cu-avatar round sm" :style="{ 'background-image': 'url(' + aiAvatar + ')'}"></view>
				</view>
				<button class="cu-btn bg-green shadow-blur" @click="sendMessage">发送</button>
			</view>
		</view> -->

    <view class="cu-bar bg-white tabbar border shop">
      <button class="action" @click="stopBtn">
        <view class="cuIcon-service text-green">
          <view class="cu-tag badge"></view>
        </view>
        <view style="margin-top: 15rpx">名师辅导</view>
      </button>
      <button class="action" @tap="showModal" data-target="Modal">
        <view
          class="cu-avatar round sm margin0"
          :style="{ 'background-image': 'url(' + aiAvatar + ')' }"
        >
        </view>
        <view class="margin-top-xs">切换Ai源</view>
      </button>
      <view class="bg-green submit" @tap="showModal" data-target="bottomModal">
        <text>向Ai提问，获取更多范例</text>
        <text class="cuIcon-right margin-left-xs"></text>
      </view>
    </view>

    <!-- 切换识别源 -->
    <!-- 弹窗确认 -->
    <view
      class="cu-modal"
      :class="modalName == 'Modal' ? 'show' : ''"
      @tap="hideModal"
    >
      <view class="cu-dialog" style="padding: 275rpx 0 70rpx">
        <view class="modal_bg">
          <view class="modal_title text-bold text-black text-dflg"
            >切换智能AI模型</view
          >
        </view>
        <view class="modal_main">
          <view class="nav-list margin-top">
            <view class="cu-list grid col-3 no-border padding-lr">
              <view class="cu-item" @click="switchBtn(1)">
                <view>
                  <image
                    src="https://cdn.zhoukaiwen.com/kimilogo.svg"
                    mode="widthFix"
                    style="width: 100rpx"
                  ></image>
                </view>
                <text>Kimi</text>
              </view>
              <view class="cu-item" @click="switchBtn(2)">
                <view>
                  <image
                    src="https://cdn.zhoukaiwen.com/deepseek2.svg"
                    mode="widthFix"
                    style="width: 100rpx"
                  ></image>
                </view>
                <text>DeepSeek</text>
              </view>
              <view class="cu-item" @click="switchBtn(3)">
                <view>
                  <image
                    src="https://cdn.zhoukaiwen.com/doubao.jpg"
                    mode="widthFix"
                    style="width: 100rpx"
                  ></image>
                </view>
                <text>豆包</text>
              </view>
            </view>
          </view>
          <!-- 提示语 -->
          <view class="text-center text-gray text-sm padding-top-sm"
            >Ai智能对话为第三方提供技术支持</view
          >
        </view>
      </view>
    </view>

    <!-- 底部窗口 -->
    <view
      class="cu-modal bottom-modal"
      :class="modalName == 'bottomModal' ? 'show' : ''"
      @tap="hideModal"
    >
      <view class="cu-dialog padding-bottom" @tap.stop="">
        <view class="cu-bar bg-white solid-bottom">
          <!-- <view class="action text-green"></view> -->
          <view class="action text-green2 text-sm text-bold">选择内容</view>
          <view class="action" @tap="hideModal">
            <text class="lg text-red cuIcon-close"></text>
          </view>
        </view>
        <view class="padding-sm cu-list menu">
          <view class="cu-item">
            <view class="content padding-tb-sm">
              <view> 范文范例 </view>
              <view class="text-gray text-sm text-left">
                <text class="cuIcon-infofill margin-right-xs"></text>
                使用Ai帮助，给出范文范例提供参考
              </view>
            </view>
            <view class="action">
              <button
                class="cu-btn round bg-green shadow"
                @click="sendMessage('fl')"
              >
                去提问 <text class="cuIcon-right"></text>
              </button>
            </view>
          </view>

          <!-- 模拟数据 -->
          <view class="cu-item">
            <view class="content padding-tb-sm">
              <view> 题目分析 </view>
              <view class="text-gray text-sm text-left">
                <text class="cuIcon-infofill margin-right-xs"></text>
                使用Ai帮助，给出题目分析提供参考
              </view>
            </view>
            <view class="action">
              <button
                class="cu-btn round bg-green shadow"
                @click="sendMessage('fx')"
              >
                去提问 <text class="cuIcon-right"></text>
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      messages: [], // 聊天记录
      inputMessage: "", // 输入框内容
      userAvatar: "/static/logo.jpg", // 用户头像
      aiAvatar: "https://cdn.zhoukaiwen.com/kimilogo.svg", // AI 头像
      modalName: null,
      switch: 1, //1-kimi 2-deepseek
      compositionInfo: {
        compositionId: "",
        title: "",
        compositionTitle: "",
      },
      massageConfig: {
        fl: {
          aimsg:
            "根据我的作文题目${title}，给出范文范例，我的作文要求主题是${compositionTitle}",
          userMsg: "根据我的作文题目${title}，给出范文范例",
        },
        fx: {
          aimsg:
            "根据我的作文题目${title}，给出题目分析，我的作文要求主题是${compositionTitle}",
          userMsg: "根据我的作文题目${title}，给出题目分析",
        },
      },
      avatarMap: {
        1: "https://cdn.zhoukaiwen.com/kimilogo.svg",
        2: "https://cdn.zhoukaiwen.com/deepseek2.svg",
        3: "https://cdn.zhoukaiwen.com/doubao.jpg",
      },
    };
  },
  onLoad(options) {
    this.compositionInfo = options;

    // const massageMap = uni.getStorageSync("massageMap");
    // if (massageMap) {
    //   const massageList = massageMap[this.compositionInfo.compositionId];
    //   this.messages = massageList.map((item) => ({
    //     sender: item.sender,
    //     content: item.content,
    //   }));
    // } else {
    this.messages.push({
      sender: "ai",
      aiAvatar: this.aiAvatar,
      content: "你好，我是你的智能AI小助手，有什么可以帮你的吗？",
    });
    // }
  },
  methods: {
    stopBtn() {
      uni.showToast({
        title: "暂未开通，敬请期待！",
        icon: "none",
        duration: 2000,
      });
    },
    switchBtn(type) {
      this.switch = type;
      this.hideModal();
      // uni.showToast({
      //   title: "切换成功",
      //   icon: "none",
      //   duration: 2000,
      // });
      // this.messages.push({
      //   sender: "ai",
      //   content: "你好，我是你的智能AI小助手，有什么可以帮你的吗？",
      // });
    },
    showModal(e) {
      this.modalName = e.currentTarget.dataset.target;
    },
    hideModal(e) {
      this.modalName = null;
    },
    // 请求数据
    getOpenAiChat(type) {
      this.hideModal();
      console.log("数据加载");
      let aimsg = this.massageConfig[type].aimsg;
      aimsg = aimsg.replace("${title}", this.compositionInfo.title);
      aimsg = aimsg.replace(
        "${compositionTitle}",
        this.compositionInfo?.compositionTitle || this.compositionInfo?.title
      );

      let opts;
      if (this.switch == 1) {
        opts = {
          url: "https://api.zhoukaiwen.com/api/openAi/kimi",
          method: "get",
        };
      } else if (this.switch == 2) {
        opts = {
          url: "https://api.zhoukaiwen.com/api/openAi/deepseek",
          method: "get",
        };
      } else {
        opts = {
          url: "https://api.zhoukaiwen.com/api/openAi/doubao",
          method: "get",
        };
      }
      var params = {
        message: aimsg,
        userId: "test1",
        password: "ry749DrZ",
      };
      uni.showLoading({
        title: "加载中",
      });
      uni.request({
        url: opts.url,
        method: opts.method,
        data: params,
        success: (res) => {
          console.log(res);
          uni.hideLoading();
          if (res.data.code == 200) {
            console.log(res.data.data.choices[0].message.content);
            // 模拟 AI 回复
            this.simulateAIReply(res.data.data.choices[0].message.content);
          } else {
            this.projectList = [];
          }
        },
      });
    },
    // 发送消息
    sendMessage(type) {
      // this.inputMessage = massage;
      // if (this.inputMessage.trim() === "") return;

      // 用户发送的消息
      let userMsg = this.massageConfig[type].userMsg;
      userMsg = userMsg.replace("${title}", this.compositionInfo.title);
      userMsg = userMsg.replace(
        "${compositionTitle}",
        this.compositionInfo.compositionTitle
      );
      this.messages.push({
        sender: "user",
        content: userMsg,
      });
      this.getOpenAiChat(type);
      // 清空输入框
      this.inputMessage = "";
      // https://api.zhoukaiwen.com/api/openAi/kimi?message=''
      // https://api.zhoukaiwen.com/api/openAi/deepseek?message=''
    },

    // 模拟 AI 回复（逐字显示）
    simulateAIReply(message) {
      const aiReply = message;
      const aiMessage = {
        sender: "ai",
        aiAvatar: this.avatarMap[this.switch],
        content: aiReply,
        displayText: "", // 当前显示的内容
        isTyping: true, // 是否正在输入
      };

      // 添加 AI 消息到聊天记录
      this.messages.push(aiMessage);
      this.saveMassage();

      // 使用定时器逐字显示
      let index = 0;
      const timer = setInterval(() => {
        if (index < aiReply.length) {
          aiMessage.displayText += aiReply[index];
          index++;
        } else {
          clearInterval(timer);
          aiMessage.isTyping = false; // 输入完成
        }
      }, 100); // 每 100ms 显示一个字
    },
    saveMassage() {
      let massageMap = uni.getStorageSync("massageMap");
      if (!massageMap) {
        massageMap = {};
      }
      massageMap[this.compositionInfo.compositionId] = this.messages || [];
      uni.setStorageSync("massageMap", massageMap);
    },
  },
};
</script>

<style lang="scss" scoped>
.margin0 {
  margin: 0 !important;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.chat-list {
  flex: 1;
  overflow-y: auto;
  padding: 20rpx;
}

.message {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.message.user {
  justify-content: flex-end;
}

.message.ai {
  justify-content: flex-start;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 20rpx;
}

.message.user .avatar {
  margin-right: 0;
  margin-left: 20rpx;
}

.message-content {
  max-width: 70%;
  padding: 20rpx;
  border-radius: 20rpx;
  margin-top: 5rpx;
  position: relative;
}

.user .message-content {
  background-color: #007aff;
  color: white;
}

.ai .message-content {
  background-color: #e5e5ea;
  color: black;
}

.cursor {
  animation: blink 1s infinite;
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }
}

.box {
  padding: 0 0 20upx 0;
}

.send-button {
  padding: 20rpx 40rpx;
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 10rpx;
}

/* 弹窗 */
.cu-dialog {
  background: #ffffff;
  overflow: visible;
}

.modal_bg {
  width: 100%;
  height: 400rpx;
  position: absolute;
  top: -100rpx;
  background-image: url(https://zhoukaiwen.com/img/qdpz/modal_bg.png);
  background-size: 100%;
  background-repeat: no-repeat;

  .modal_title {
    width: 300rpx;
    position: absolute;
    bottom: 30rpx;
    left: calc(50% - 150rpx);
  }
}

.modal_main {
  background-color: #ffffff;
}
</style>
