<!-- 消息通知 -->
<template>
  <view class="container">
    <cu-custom bgColor="bg-green3" :isBack="true">
      <block slot="backText"></block>
      <block slot="content">消息通知</block>
    </cu-custom>

    <scroll-view scroll-x class="bg-transparent nav">
      <view class="flex text-center">
        <view
          class="cu-item flex-sub"
          :class="index == TabCur ? 'text-green3 cur' : ''"
          v-for="(item, index) in navList"
          :key="index"
          @tap="tabSelect"
          :data-id="index"
        >
          {{ item.name }}
        </view>
      </view>
    </scroll-view>

    <view class="cu-list menu-avatar solid-top">
      <view
        v-for="(item, index) in messageList"
        :key="index"
        class="cu-item cur"
        @tap="goToDetail(item.id)"
      >
        <view class="content">
          <view class="flex justify-between">
            <view class="text-cut">
              <text class="text-bold">{{ item.title }}</text>
              <text
                class="text-sm text-grey margin-left-sm"
                v-if="item.messageType === 'school'"
                >[{{ item.schoolName }}]</text
              >
            </view>
            <view>
              <text class="margin-left-sm text-sm text-grey">{{
                formatTime(item.createTime)
              }}</text>
            </view>
          </view>
          <view class="text-gray text-smdf flex">
            <view class="text-cut2">{{ item.content }}</view>
          </view>
        </view>
      </view>
      <view
        style="
          height: 100rpx;
          width: 100%;
          text-align: center;
          line-height: 100rpx;
        "
        v-if="messageList.length == 0"
      >
        暂无消息
      </view>
    </view>
  </view>
</template>

<script>
import request from "@/common/request.js";
export default {
  data() {
    return {
      TabCur: 0,
      scrollLeft: 0,
      navList: [
        {
          name: "系统消息",
          type: "system",
        },
        {
          name: "学校消息",
          type: "school",
        },
      ],
      messageList: [],
    };
  },
  onLoad(option) {
    this.getList();
  },
  methods: {
    tabSelect(e) {
      this.TabCur = e.currentTarget.dataset.id;
      this.scrollLeft = (e.currentTarget.dataset.id - 1) * 60;
      this.getList();
    },
    formatTime(time) {
      if (!time) return "";
      const date = new Date(time);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
        2,
        "0"
      )}-${String(date.getDate()).padStart(2, "0")} ${String(
        date.getHours()
      ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
    },
    getList() {
      let opts = {
        url: "bse_message/page",
        method: "get",
      };
      request
        .httpTokenRequest(opts, {
          page: 1,
          pageSize: 10,
          messageType: this.navList[this.TabCur].type,
        })
        .then((res) => {
          console.log(res);
          if (res.code == 200) {
            this.messageList = res.data.records;
          } else {
            console.log("数据请求错误～");
          }
        });
    },
    goToDetail(id) {
      uni.navigateTo({
        url: `/pages/me/news-detail?id=${id}`,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  // background-size: 100% 350px;
  // background-repeat: no-repeat;
  // /* background-size: cover; */
  // height: 760rpx;
  // width: 750rpx;
  // overflow: hidden;
  // flex-direction: column;
  // padding: 20rpx;
  // position: absolute;
  // top: 0;
  // left: 0;
  // background-image: url(https://cdn.zhoukaiwen.com/cewen_me_bg.png);
}
.cu-list.menu-avatar > .cu-item .content {
  width: 100% !important;
  padding: 20rpx 30rpx;
}
.cu-list.menu-avatar > .cu-item {
  height: auto !important;
  min-height: 160rpx;
}
.cu-list.menu-avatar > .cu-item .content {
  left: 0 !important;
}
</style>
