<!-- 蓝色简洁登录页面 -->
<template>
  <view class="t-login">
    <!-- 页面装饰图片 -->
    <image
      class="img-a"
      src="https://zhoukaiwen.com/img/loginImg/2.png"
    ></image>
    <image
      class="img-b"
      src="https://zhoukaiwen.com/img/loginImg/3.png"
    ></image>
    <!-- logo -->
    <image
      class="cw_logo"
      src="https://cdn.zhoukaiwen.com/cw_loginlogo_1.png"
      mode="widthFix"
    ></image>
    <!-- 标题 -->
    <view class="t-b">请登录</view>
    <view class="t-b2">测文网小程序欢迎您</view>
    <form class="cl">
      <view class="t-a">
        <image src="https://cdn.zhoukaiwen.com/icon_zh.svg"></image>
        <view class="line"></view>
        <input
          placeholder="请输入账号"
          maxlength="50"
          v-model="formData.userName"
        />
      </view>
      <view class="t-a">
        <image src="https://zhoukaiwen.com/img/loginImg/yz.png"></image>
        <view class="line"></view>
        <input
          type="password"
          maxlength="20"
          placeholder="请输入密码"
          v-model="formData.password"
        />
      </view>
      <button @tap="login()">登 录</button>

      <view class="t-f" @tap="goRegister">没有账号？立即注册</view>
    </form>
    <!-- <view class="t-f margin-top">
			老师登录
		</view> -->
  </view>
</template>
<script>
import request from "@/common/request.js";
export default {
  data() {
    return {
      title: "欢迎回来！", //填写logo或者app名称，也可以用：欢迎回来，看您需求
      phone: "", //手机号码
      yzm: "", //验证码
      formData: {
        userName: "",
        password: "",
      },
    };
  },
  onLoad() {},
  methods: {
    //当前登录按钮操作
    login() {
      var that = this;
      if (!that.formData.userName) {
        uni.showToast({ title: "请输入学号", icon: "none" });
        return;
      }
      if (!that.formData.password) {
        uni.showToast({ title: "请输入密码", icon: "none" });
        return;
      }
      request
        .httpRequest(
          {
            url: "auth/wx_login",
            method: "post",
          },
          {
            userName: that.formData.userName,
            password: that.formData.password,
          }
        )
        .then(async (res) => {
          const { code, data, message } = res;
          if (code === 200) {
            uni.showToast({ title: "登录成功！", icon: "none" });
            uni.setStorageSync("token", data.token);
            await that.getUserInfo();
            setTimeout(() => {
              uni.reLaunch({
                url: "/pages/home/<USER>",
              });
            }, 200);
          } else {
            uni.showToast({ title: message, icon: "none" });
          }
        })
        .catch((err) => {
          uni.showToast({ title: "登录失败！", icon: "none" });
        });
    },
    async getUserInfo() {
      const res = await request.httpTokenRequest({
        url: "auth/user_info",
        method: "get",
      });
      if (res.code === 200) {
        uni.setStorageSync("userInfo", res.data);
      }
    },
    //等三方微信登录
    wxLogin() {
      uni.showToast({ title: "微信登录", icon: "none" });
    },
    //第三方支付宝登录
    zfbLogin() {
      uni.showToast({ title: "支付宝登录", icon: "none" });
    },
    // 跳转到注册页面
    goRegister() {
      uni.navigateTo({
        url: "/pages/user/register",
      });
    },
  },
};
</script>
<style>
.img-a {
  position: absolute;
  width: 100%;
  top: -150rpx;
  right: 0;
}
.img-b {
  position: absolute;
  width: 50%;
  bottom: 0;
  left: -50rpx;
  /* margin-bottom: -200rpx; */
}
.t-login {
  width: 650rpx;
  margin: 0 auto;
  font-size: 28rpx;
  color: #000;
}
.cw_logo {
  width: 150rpx;
  margin: 150rpx 0 30rpx -20rpx;
}

.t-login button {
  font-size: 28rpx;
  background: #21b287;
  color: #fff;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 50rpx;
  box-shadow: 0 5px 7px 0 rgba(86, 119, 252, 0.2);
}

.t-login input {
  padding: 0 20rpx 0 120rpx;
  height: 90rpx;
  line-height: 90rpx;
  margin-bottom: 50rpx;
  background: #f8f7fc;
  border: 1px solid #e9e9e9;
  font-size: 28rpx;
  border-radius: 50rpx;
}

.t-login .t-a {
  position: relative;
}

.t-login .t-a image {
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  left: 40rpx;
  top: 28rpx;
  /* border-right: 2rpx solid #dedede; */
  margin-right: 20rpx;
}
.t-login .t-a .line {
  width: 2rpx;
  height: 40rpx;
  background-color: #dedede;
  position: absolute;
  top: 28rpx;
  left: 98rpx;
}

.t-login .t-b {
  text-align: left;
  font-size: 46rpx;
  color: #000;
  /* padding: 300rpx 0 30rpx 0; */
  font-weight: bold;
}
.t-login .t-b2 {
  text-align: left;
  font-size: 32rpx;
  color: #aaaaaa;
  padding: 10rpx 0 100rpx 0;
}

.t-login .t-d {
  text-align: center;
  color: #999;
  margin: 80rpx 0;
}

.t-login .t-f {
  text-align: center;
  color: #21b287;
  margin: 30rpx 0;
}

.t-login .t-g {
  float: left;
  width: 50%;
}

.t-login .uni-input-placeholder {
  color: #000;
}
</style>
