<!-- 消息详情 -->
<template>
  <view class="container">
    <cu-custom bgColor="bg-green3" :isBack="true">
      <block slot="backText"></block>
      <block slot="content">消息详情</block>
    </cu-custom>

    <view class="padding-lr padding-tb">
      <view class="bg-white padding radius-lg">
        <view class="text-xl text-bold margin-bottom">{{ detail.title }}</view>
        <view class="flex justify-between align-center margin-bottom-sm">
          <view class="text-sm text-grey">
            <!-- <text v-if="detail.messageType === 'school'"
              >[{{ detail.schoolName }}]</text
            > -->
            <text>{{ formatTime(detail.createTime) }}</text>
          </view>
          <view class="text-sm text-grey">{{
            detail.messageType === "system" ? "系统消息" : "学校消息"
          }}</view>
        </view>
        <view class="text-content">
          <text>{{ detail.content }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import request from "@/common/request.js";

export default {
  data() {
    return {
      id: "",
      detail: {},
    };
  },
  onLoad(option) {
    if (option.id) {
      this.id = option.id;
      this.getDetail();
    }
  },
  methods: {
    formatTime(time) {
      if (!time) return "";
      const date = new Date(time);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
        2,
        "0"
      )}-${String(date.getDate()).padStart(2, "0")} ${String(
        date.getHours()
      ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
    },
    getDetail() {
      let opts = {
        url: `bse_message/${this.id}`,
        method: "get",
      };
      request.httpTokenRequest(opts).then((res) => {
        if (res.code == 200) {
          this.detail = res.data;
        } else {
          uni.showToast({
            title: "获取详情失败",
            icon: "none",
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.text-content {
  line-height: 1.6;
  color: #333;
  font-size: 28rpx;
}
</style>
