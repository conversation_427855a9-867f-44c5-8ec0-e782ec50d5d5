<!-- 修改密码页面 -->
<template>
  <view class="t-login">
    <!-- 页面装饰图片 -->
    <image
      class="img-a"
      src="https://zhoukaiwen.com/img/loginImg/2.png"
    ></image>
    <image
      class="img-b"
      src="https://zhoukaiwen.com/img/loginImg/3.png"
    ></image>
    <!-- logo -->
    <image
      class="cw_logo"
      src="https://cdn.zhoukaiwen.com/cw_loginlogo_1.png"
      mode="widthFix"
    ></image>
    <!-- 标题 -->
    <view class="t-b">修改密码</view>
    <view class="t-b2">请输入您的新密码</view>
    <form class="cl">
      <view class="t-a">
        <image src="https://cdn.zhoukaiwen.com/icon_lock.svg"></image>
        <view class="line"></view>
        <input
          type="password"
          maxlength="20"
          placeholder="请输入原密码"
          v-model="form.oldPassword"
        />
      </view>

      <view class="t-a">
        <image src="https://cdn.zhoukaiwen.com/icon_lock.svg"></image>
        <view class="line"></view>
        <input
          type="password"
          maxlength="20"
          placeholder="请输入新密码"
          v-model="form.newPassword"
        />
      </view>

      <view class="t-a">
        <image src="https://cdn.zhoukaiwen.com/icon_lock.svg"></image>
        <view class="line"></view>
        <input
          type="password"
          maxlength="20"
          placeholder="请确认新密码"
          v-model="form.confirmPassword"
        />
      </view>

      <view class="tips-text">密码修改成功后，下次登录需使用新密码</view>

      <button @tap="changePassword()">确认修改</button>

      <view class="t-f" @tap="goBack">返回</view>
    </form>
  </view>
</template>

<script>
import request from "@/common/request.js";
export default {
  data() {
    return {
      form: {
        oldPassword: "",
        newPassword: "",
        confirmPassword: "",
      },
    };
  },
  onLoad() {
    // 检查是否已登录
    const token = uni.getStorageSync("token");
    if (!token) {
      uni.showToast({
        title: "请先登录",
        icon: "none",
      });
      setTimeout(() => {
        uni.navigateTo({
          url: "/pages/user/login",
        });
      }, 1500);
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    // 修改密码
    changePassword() {
      // 表单验证
      if (!this.form.oldPassword) {
        return uni.showToast({ title: "请输入原密码", icon: "none" });
      }
      if (!this.form.newPassword) {
        return uni.showToast({ title: "请输入新密码", icon: "none" });
      }
      if (!this.form.confirmPassword) {
        return uni.showToast({ title: "请确认新密码", icon: "none" });
      }
      if (this.form.newPassword !== this.form.confirmPassword) {
        return uni.showToast({ title: "两次输入的新密码不一致", icon: "none" });
      }
      if (this.form.oldPassword === this.form.newPassword) {
        return uni.showToast({ title: "新密码不能与原密码相同", icon: "none" });
      }

      // 提交修改密码请求
      request
        .httpTokenRequest(
          {
            url: "user/change_password",
            method: "post",
          },
          {
            oldPassword: this.form.oldPassword,
            newPassword: this.form.newPassword,
          }
        )
        .then((res) => {
          if (res.code === 200) {
            uni.showToast({
              title: "密码修改成功",
              duration: 1500,
            });
            setTimeout(function () {
              // 清除登录信息，重新登录
              uni.removeStorageSync("token");
              uni.removeStorageSync("userInfo");
              uni.navigateTo({
                url: "/pages/user/login",
              });
            }, 1200);
          } else {
            uni.showToast({
              title: res.message || "修改失败",
              icon: "none",
            });
          }
        })
        .catch((err) => {
          uni.showToast({
            title: "修改失败，请稍后重试",
            icon: "none",
          });
        });
    },
  },
};
</script>

<style>
.img-a {
  position: absolute;
  width: 100%;
  top: -150rpx;
  right: 0;
}
.img-b {
  position: absolute;
  width: 50%;
  bottom: 0;
  left: -50rpx;
}
.t-login {
  width: 650rpx;
  margin: 0 auto;
  font-size: 28rpx;
  color: #000;
  padding-bottom: 50rpx;
}
.cw_logo {
  width: 150rpx;
  margin: 150rpx 0 30rpx -20rpx;
}

.t-login button {
  font-size: 28rpx;
  background: #21b287;
  color: #fff;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 50rpx;
  box-shadow: 0 5px 7px 0 rgba(86, 119, 252, 0.2);
}

.t-login input {
  padding: 0 20rpx 0 120rpx;
  height: 90rpx;
  line-height: 90rpx;
  margin-bottom: 30rpx;
  background: #f8f7fc;
  border: 1px solid #e9e9e9;
  font-size: 28rpx;
  border-radius: 50rpx;
}

.t-login .t-a {
  position: relative;
}

.t-login .t-a image {
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  left: 40rpx;
  top: 28rpx;
  margin-right: 20rpx;
}
.t-login .t-a .line {
  width: 2rpx;
  height: 40rpx;
  background-color: #dedede;
  position: absolute;
  top: 28rpx;
  left: 98rpx;
}

.t-login .t-b {
  text-align: left;
  font-size: 46rpx;
  color: #000;
  font-weight: bold;
}
.t-login .t-b2 {
  text-align: left;
  font-size: 32rpx;
  color: #aaaaaa;
  padding: 10rpx 0 50rpx 0;
}

.t-login .t-f {
  text-align: center;
  color: #21b287;
  margin: 30rpx 0;
}

.tips-text {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  margin: 20rpx 0;
}
</style>
