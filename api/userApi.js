/**
 * 用户相关API接口 - 简化版
 * 快速好用的用户请求封装，支持用户信息缓存
 */

import httpClient from "./http.js";

/**
 * 用户API类
 */
class UserAPI {
  constructor() {
    this.cachedUserInfo = null; // 内存缓存用户信息
  }

  /**
   * 用户登录
   */
  async login(credentials) {
    const response = await httpClient.post("auth/wx_login", credentials, {
      needAuth: false,
      loadingText: "登录中...",
    });

    // 登录成功后保存token和用户信息
    if (response.data && response.data.token) {
      uni.setStorageSync("token", response.data.token);

      // 获取并缓存用户信息
      await this.refreshUserInfo();
    }

    return response;
  }

  /**
   * 用户注册
   */
  async register(userInfo) {
    return await httpClient.post("wx/register", userInfo, {
      needAuth: false,
      loadingText: "注册中...",
    });
  }

  /**
   * 获取用户信息（优先从缓存获取）
   */
  async getUserInfo(forceRefresh = false) {
    // 如果有缓存且不强制刷新，直接返回缓存
    if (this.cachedUserInfo && !forceRefresh) {
      return { data: this.cachedUserInfo };
    }

    // 从服务器获取最新用户信息
    const response = await httpClient.get(
      "auth/user_info",
      {},
      {
        showLoading: false,
      }
    );

    // 更新缓存
    if (response.data) {
      this.cachedUserInfo = response.data;
      uni.setStorageSync("userInfo", response.data);
    }

    return response;
  }

  /**
   * 刷新用户信息（强制从服务器获取）
   */
  async refreshUserInfo() {
    const result = await this.getUserInfo(true);

    // 触发全局用户信息更新事件
    if (result && result.data) {
      this.notifyUserInfoUpdated(result.data);
    }

    return result;
  }

  /**
   * 更新用户信息
   */
  async updateUserInfo(userInfo) {
    const response = await httpClient.post("wx/wxRegisterOrLogin", userInfo, {
      loadingText: "保存中...",
    });

    // 更新缓存和本地存储
    if (response.data) {
      this.cachedUserInfo = response.data;
      uni.setStorageSync("userInfo", response.data);

      // 触发全局用户信息更新事件
      this.notifyUserInfoUpdated(response.data);
    }

    return response;
  }

  /**
   * 微信登录 - 获取AccessToken
   */
  async getWxAccessToken(code) {
    return await httpClient.post(
      "wx/getAccessToken",
      { code },
      {
        needAuth: false,
        loadingText: "微信登录中...",
      }
    );
  }

  /**
   * 微信注册或登录
   */
  async wxRegisterOrLogin(wxUserInfo) {
    const response = await httpClient.post("wx/wxRegisterOrLogin", wxUserInfo, {
      needAuth: false,
      loadingText: "处理中...",
    });

    // 更新缓存和本地存储
    if (response.data) {
      this.cachedUserInfo = response.data;
      uni.setStorageSync("userInfo", response.data);

      // 触发全局用户信息更新事件
      this.notifyUserInfoUpdated(response.data);
    }

    return response;
  }

  /**
   * 用户登出
   */
  logout() {
    // 清除缓存
    this.cachedUserInfo = null;
    uni.clearStorageSync();
    uni.showToast({
      title: "已退出登录",
      icon: "success",
      complete: () => {
        setTimeout(() => {
          uni.reLaunch({ url: "/pages/user/login" });
        }, 1500);
      },
    });
  }

  /**
   * 检查登录状态
   */
  isLoggedIn() {
    const token = uni.getStorageSync("token");
    const userInfo = uni.getStorageSync("userInfo");
    return !!(token && userInfo);
  }

  /**
   * 获取本地用户信息（优先从缓存获取）
   */
  getLocalUserInfo() {
    // 优先返回内存缓存
    if (this.cachedUserInfo) {
      return this.cachedUserInfo;
    }

    // 从本地存储获取并缓存
    const userInfo = uni.getStorageSync("userInfo");
    if (userInfo) {
      this.cachedUserInfo = userInfo;
      return userInfo;
    }

    return null;
  }

  /**
   * 初始化用户信息缓存（应用启动时调用）
   */
  initUserCache() {
    const userInfo = uni.getStorageSync("userInfo");
    if (userInfo) {
      this.cachedUserInfo = userInfo;
    }
  }

  /**
   * 触发全局用户信息更新事件
   * @param {Object} userInfo 更新后的用户信息
   */
  notifyUserInfoUpdated(userInfo) {
    try {
      // 使用uni.$emit触发全局事件
      if (typeof uni.$emit === "function") {
        uni.$emit("userInfoUpdated", userInfo);
        console.log("已触发用户信息更新事件:", userInfo);
      }
    } catch (error) {
      console.error("触发用户信息更新事件失败:", error);
    }
  }

  /**
   * 手动触发用户信息更新通知（用于外部调用）
   */
  triggerUserInfoUpdate() {
    if (this.cachedUserInfo) {
      this.notifyUserInfoUpdated(this.cachedUserInfo);
    }
  }

  /**
   * 强制登录检查
   */
  requireLogin() {
    if (!this.isLoggedIn()) {
      uni.showToast({
        title: "请先登录",
        icon: "none",
        complete: () => {
          setTimeout(() => {
            uni.reLaunch({ url: "/pages/user/login" });
          }, 1500);
        },
      });
      return false;
    }
    return true;
  }
}

// 创建用户API实例
const userAPI = new UserAPI();

export default userAPI;
export { UserAPI };
