<template>
	<view class="components-home" style="position: relative;">
		<view class="topBg" style="background-image: url(https://cdn.zhoukaiwen.com/cewen_me_bg.png)">
			<view class="cu-custom">
				<view class="fixed" :style="style" style="justify-content: center; align-content: center">
					<view class="text-white text-bold text-lg text-center" style="position: relative"
						:style="[{ top: StatusBar + 'px' }]">
						<text @click="goBack" class="cuIcon-back text-left"
							style="position: absolute; left: 0; top: 6rpx"></text>
						<text class="">作业详情</text>
					</view>
				</view>
			</view>
		</view>
		
		<view class="mainBox" :style="[{ top: CustomBar + 10 + 'px' }]">
			<view class="carBox">
				<view class="bg-white" style="border-radius: 20rpx; overflow: hidden;">
					<!-- 基础信息标题 -->
					<view class="cu-bar bg-white">
						<view class="action sub-title">
							<text class="text-xl text-bold text-green3 text-shadow">基础信息</text>
							<text class="text-ABC text-green3">jichuxinxi</text>
						</view>
					</view>
								
					<view class="padding-lr padding-bottom bg-white">
						<view class="base-info">
							<!-- 标题区域 -->
							<view class="text-black text-bold text-xl">《{{ composition.compositionTitle }}》</view>
								
							<!-- 标签组 -->
							<view class="tag-group">
								<view class="tag-item">
									<text class="cuIcon-title text-green3"></text>
									<text>{{ composition.compositionWenTi }}</text>
								</view>
								<view class="tag-item">
									<text class="cuIcon-write text-green3"></text>
									<text>{{ composition.compositionWordCount }}字</text>
								</view>
								<view class="tag-item">
									<text class="cuIcon-rank text-green3"></text>
									<text>满分{{ composition.compositionFullScore }}分</text>
								</view>
							</view>
								
							<!-- 时间信息 -->
							<view class="time-info">
								<view class="time-value">
									<text class="cuIcon-time"></text>
									<text>{{ composition.compositionBeginTime | timeF }}</text>
									<text class="separator">至</text>
									<text>{{ composition.compositionEndTime | timeF }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			
				
				<view class="bg-white margin-top" style="border-radius: 20rpx; overflow: hidden;">
					<view class="cu-bar bg-white ">
						<view class="action sub-title">
							<text class="text-xl text-bold text-green3 text-shadow">上传作文&测评文章</text>
							<text class="text-ABC text-green3">shangchuanzuowen</text>
						</view>
					</view>
					
					<view class="cu-form-group">
						<view class="title text-bold text-black">作文标题</view>
						<input class="text-right" v-model="compositionInfo.title" placeholder="请填写作文标题" name="input"></input>
					</view>
								
					<view class="cu-form-group">
						<view class="title text-bold text-black">作者姓名</view>
						<input class="text-right" v-model="compositionInfo.author" placeholder="请填写作者姓名" name="input"></input>
					</view>
								
					<view class="cu-bar bg-white" style="border-top: 1rpx solid #eee;">
						<view class="action text-bold text-black">
							图片上传
						</view>
						<view class="action">
							{{compositionInfo.images.length}}/4
						</view>
					</view>
					<view class="cu-form-group">
						<view class="grid col-4 grid-square flex-sub">
							<view class="bg-img" v-for="(item,index) in compositionInfo.images" :key="index" @tap="ViewImage"
								:data-url="item">
								<image :src="`${webUrl}/${item}`" mode="aspectFill"></image>
								<view class="cu-tag bg-red" @tap.stop="DelImg" :data-index="index">
									<text class='cuIcon-close'></text>
								</view>
							</view>
							<view class="solids" @tap="ChooseImage" v-if="compositionInfo.images.length<4">
								<text class='cuIcon-cameraadd'></text>
							</view>
						</view>
					</view>
					<view class="padding-lr padding-bottom-sm bg-white text-smdf text-gray">温馨提醒：请按文章顺序上传图片</view>
								
								
					<view class="padding flex justify-between margin-top-xs">
						<button @click="viewDrawToCanvas(0)" class="cu-btn bg-green3 lg" style="width: 48%;">预览文章</button>
						<button v-if="userInfo.evaluationNumber >0" @click="viewDrawToCanvas(2)"
							class="cu-btn bg-green3 lg" style="width: 48%;">文章测评</button>
						<button v-else class="cu-btn bg-green3 lg" style="width: 48%;">请先获得次数</button>
					</view>
					
				</view>
				
			
				
			
				<!-- 画板 -->
				<canvas class='canvas-poster' canvas-id='canvasposter' :style="{'height':canvasHeight+'px'}"></canvas>
			</view>
			
			<view style="height: 150rpx;width: 1rpx;"></view>
		</view>
	</view>
</template>

<script>
	import request,{webUrl} from "@/common/request.js";
	import moment from "@/components/moment/index.js"; // 格式化时间 插件
	export default {
		data() {
			return {
				webUrl,
				CustomBar: this.CustomBar,
				// 作文信息
				compositionInfo: {
					title: "",
					author: "原创",
					images: [],
				},
				composition: {},
				// 用户信息
				userInfo: null,
				// canvas相关
				canvasHeight: 0,
			};
		},
		computed: {
			style() {
				const StatusBar = this.StatusBar;
				const CustomBar = this.CustomBar;
				const bgImage = this.bgImage;
				const style = `height:${CustomBar}px;padding-top:${StatusBar}px;`;
				if (this.bgImage) {
					style = `${style}background-image:url(${bgImage});`;
				}
				return style;
			},
		},
		onLoad(option) {
			this.getDetailJob(option.id);
			this.getUserInfo();
		},
		methods: {
			// 页面初始化
			async getDetailJob(id) {
				uni.showLoading({
					title: "加载中",
				});
				try {
					const res = await request.httpTokenRequest({
						url: `biz_composition_release/${id}`,
						method: "GET",
					});
					this.composition = res.data || {};
				} finally {
					uni.hideLoading();
				}
			},

			// 获取用户信息
			getUserInfo() {
				const userInfo = uni.getStorageSync("userInfo");
				this.userInfo = userInfo;
				this.compositionInfo.author = userInfo.nickName;
			},

			// 图片处理相关方法
			async handleImageUpload(tempFilePaths) {
				for (let path of tempFilePaths) {
					try {
						const result = await this.uploadFile(path);
						const res = JSON.parse(result);
						if (res.code === 200) {
							if (this.compositionInfo.images.length) {
								this.compositionInfo.images.push(res.data);
							} else {
								this.compositionInfo.images = [res.data];
							}
						}
					} catch (error) {
						console.error("上传失败:", error);
						uni.showToast({
							title: "图片上传失败",
							icon: "none",
						});
					}
				}
			},

			ChooseImage() {
				uni.chooseImage({
					count: 4,
					sizeType: ["original", "compressed"],
					sourceType: ["album", "camera"],
					success: (res) => {
						this.handleImageUpload(res.tempFilePaths);
					},
				});
			},

			uploadFile(filePath) {
				const token = uni.getStorageSync("token");
				return new Promise((resolve, reject) => {
					uni.uploadFile({
						url: `${request.baseUrl}mon_file/upload-file`,
						filePath,
						name: "file",
						header: {
							Authorization: `Bearer ${token}`,
						},
						success: (res) => {
							resolve(res.data);
						},
						fail: reject,
					});
				});
			},

			// 图片预览和删除
			ViewImage(e) {
				uni.previewImage({
					urls: this.compositionInfo.images,
					current: e.currentTarget.dataset.url,
				});
			},

			DelImg(e) {
				uni.showModal({
					title: "删除照片",
					content: "确定要删除图片吗？",
					success: (res) => {
						if (res.confirm) {
							this.compositionInfo.images.splice(
								e.currentTarget.dataset.index,
								1
							);
						}
					},
				});
			},

			// 表单验证
			validateForm(checkTheme = true) {
				if (!this.compositionInfo.title) {
					uni.showToast({
						title: "请填写文章标题",
						icon: "none"
					});
					return false;
				}
				if (!this.compositionInfo.author) {
					uni.showToast({
						title: "请填写作者姓名",
						icon: "none"
					});
					return false;
				}
				if (!this.compositionInfo.images.length) {
					uni.showToast({
						title: "请先上传文章图片",
						icon: "none"
					});
					return false;
				}
				return true;
			},

			// Canvas绘制
			async viewDrawToCanvas(btnType) {
				if (!this.validateForm(btnType !== 0)) return;

				uni.showLoading({
					title: "请稍等，图片合成中..."
				});

				try {
					const {
						tempFilePath
					} = await this.drawCanvasAndSave();

					if (btnType === 0) {
						uni.previewImage({
							urls: [tempFilePath],
							current: 0,
						});
					} else {
						this.navigateToEdit(tempFilePath);
					}
				} catch (error) {
					console.error("Canvas处理失败:", error);
					uni.showToast({
						title: "图片处理失败",
						icon: "none",
					});
				} finally {
					uni.hideLoading();
				}
			},

			async drawCanvasAndSave() {
				const ctx = uni.createCanvasContext("canvasposter", this);
				let newHeight = 0;

				ctx.clearRect(0, 0, 0, 0);
				ctx.draw();

				for (let i = 0; i < this.compositionInfo.images.length; i++) {
					await new Promise((resolve) => {
						uni.getImageInfo({
							src: `${webUrl}/${this.compositionInfo.images[i]}`,
							complete: (image) => {
								const width = 350;
								const height = Math.ceil(image.height * (width / image.width));
								newHeight += height;
								ctx.drawImage(image.path, 0, newHeight - height, width, height);
								ctx.draw(true);
								this.canvasHeight = newHeight;
								resolve();
							},
						});
					});
				}

				return new Promise((resolve) => {
					setTimeout(() => {
						uni.canvasToTempFilePath({
								canvasId: "canvasposter",
								success: resolve,
							},
							this
						);
					}, 600);
				});
			},

			async navigateToEdit(tempFilePath) {
				const res = await this.subNum();
				if (res) return;
				const params = {
					imgUrl: tempFilePath,
					compositionPath: this.compositionInfo.images.join(","),
					title: this.compositionInfo.title,
					author: this.compositionInfo.author,
					compositionId: this.composition.id,
					requireTitle: this.composition.compositionTitle,
					requireWords: this.composition.compositionWordCount,
					requireWenTi: this.composition.compositionWenTi,
					fullScore: this.composition.compositionFullScore,
					evaluateSources: 1,
					grade: this.composition.gradeName,
					did: this.composition.did,
				};
				const url = `/pages/job/recognition-score?${Object.entries(params)
        .map(([key, value]) => `${key}=${value}`)
        .join("&")}`;
				uni.navigateTo({
					url
				});
			},
			subNum() {
				return new Promise((resolve, reject) => {
					let opts = {
						url: "sys_user/updateTimes",
						method: "POST",
					};
					let params = {
						"id": this.userInfo.id,
						"type": 'sub',
						"rowType": 'evaluationNumber',
						"numbers": 1,
						remark: `测评作文《${this.compositionInfo.title}》消耗1次测评次数`
					};
					request.httpTokenRequest(opts, params).then((res) => {
						if (res.data < 0) {
							uni.showToast({
								title: "次数不足，请先充值！",
								duration: 1300,
								icon: "none"
							});
							this.userInfo.evaluationNumber = res.data;
							uni.setStorageSync("userInfo", this.userInfo);
							return reject(true);
						}
						return resolve(false);
					})
				})
			},
			goBack() {
				uni.navigateBack({
					delta: 1,
				});
			},
		},
		filters: {
			timeF: function(time) {
				let mm = Number(time);
				let ss = moment(mm).format("YYYY.MM.DD HH:mm");
				return ss;
			},
		},
	};
</script>

<style lang="scss" scoped>
	.topBg {
		background-size: 100% 100%;
		/* background-size: cover; */
		height: 760rpx;
		width: 750rpx;
		overflow: hidden;
		flex-direction: column;
		padding: 20rpx;
		position: absolute;
		top: 0;
		left: 0;
	}
	
	.mainBox {
		width: 700rpx;
		position: absolute;
		left: 25rpx;
	}
	
	/* 绘制图片canvas样式 */
	.canvas-poster {
		position: fixed;
		width: 350px;
		top: 100%;
		left: 100%;
	}

	.form-group {
		.form-item {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 24rpx 0;
			border-bottom: 1rpx solid #eee;

			&:last-child {
				border-bottom: none;
			}

			.label {
				display: flex;
				align-items: center;
				font-size: 30rpx;
				color: #333;
				font-weight: bold;

				text {
					&:first-child {
						font-size: 32rpx;
						margin-right: 12rpx;
					}
				}
			}

			.picker-value {
				font-size: 28rpx;
				color: #555;
				padding: 0 20rpx;
			}

			.input-value {
				font-size: 28rpx;
				color: #555;
				text-align: right;
				padding: 0 20rpx;
			}
		}
	}

	.base-info {
		.title-section {
			margin-bottom: 30rpx;

			.main-title {
				font-size: 40rpx;
				font-weight: bold;
				color: #333;
				line-height: 1.4;
			}

			.divider {
				width: 120rpx;
				height: 6rpx;
				background: linear-gradient(90deg, #2979ff, #5cadff);
				border-radius: 3rpx;
				margin: 20rpx 0;
			}
		}

		.tag-group {
			display: flex;
			align-items: center;
			margin: 20rpx 0;

			.tag-item {
				display: flex;
				align-items: center;
				margin-right: 20rpx;
				background: #f8f8f8;
				padding: 6rpx 16rpx;
				border-radius: 6rpx;

				.cuIcon-title,
				.cuIcon-write,
				.cuIcon-rank {
					font-size: 28rpx;
				}

				text {
					&:first-child {
						margin-right: 6rpx;
					}

					&:last-child {
						font-size: 26rpx;
						color: #666;
					}
				}
			}
		}

		.select-btn {
			display: flex;
			align-items: center;
			justify-content: center;
			margin-top: 30rpx;
			padding: 20rpx 0;
			background: #f8f8f8;
			border-radius: 8rpx;
			color: #2979ff;
			font-size: 28rpx;

			.cuIcon-edit {
				margin-right: 8rpx;
			}

			&:active {
				opacity: 0.8;
			}
		}

		.time-info {
			margin-top: 30rpx;

			.time-value {
				display: flex;
				align-items: center;
				color: #666;
				font-size: 28rpx;
				letter-spacing: 2rpx;

				.cuIcon-time {
					color: #2979ff;
					font-size: 36rpx;
					margin-right: 16rpx;
				}

				text {
					&:not(.separator) {
						margin: 0 8rpx;
						color: #333;
						font-weight: 500;
					}

					&.separator {
						margin: 0 20rpx;
						color: #999;
						font-weight: normal;
					}
				}
			}
		}
	}
</style>