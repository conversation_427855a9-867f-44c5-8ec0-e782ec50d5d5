/**
 * Transform record to option
 *
 * @example
 *   ```ts
 *   const record = {
 *     key1: 'label1',
 *     key2: 'label2'
 *   };
 *   const options = transformRecordToOption(record);
 *   // [
 *   //   { value: 'key1', label: 'label1' },
 *   //   { value: 'key2', label: 'label2' }
 *   // ]
 *   ```;
 *
 * @param record
 */
export function transformRecordToOption(record, type = "string") {
  return Object.entries(record).map(([value, label]) => ({
    value: type === "number" ? Number(value) : value,
    text: label,
  }));
}

export const CompositionSubmittedStatus = {
  0: "未完成",
  1: "已修改",
  2: "已提交",
};

// 作文分类
export const EssayCategories = [
  {
    text: "叙事记叙文",
    value: "叙事记叙文",
  },
  {
    text: "写人记叙文",
    value: "写人记叙文",
  },
  {
    text: "写景记叙文",
    value: "写景记叙文",
  },
  {
    text: "状物记叙文",
    value: "状物记叙文",
  },
  {
    text: "想象记叙文",
    value: "想象记叙文",
  },
  {
    text: "其它记叙文",
    value: "其它记叙文",
  },
  {
    text: "议论文",
    value: "议论文",
  },
  {
    text: "事理说明文",
    value: "事理说明文",
  },
  {
    text: "事物说明文",
    value: "事物说明文",
  },
  {
    text: "书信",
    value: "书信",
  },
  {
    text: "散文",
    value: "散文",
  },
  {
    text: "新闻",
    value: "新闻",
  },
  {
    text: "通讯",
    value: "通讯",
  },
  {
    text: "小论文",
    value: "小论文",
  },
  {
    text: "演讲稿",
    value: "演讲稿",
  },
  {
    text: "读后感",
    value: "读后感",
  },
  {
    text: "观后感",
    value: "观后感",
  },
  {
    text: "倡议书",
    value: "倡议书",
  },
  {
    text: "表扬稿",
    value: "表扬稿",
  },
  {
    text: "学习心得",
    value: "学习心得",
  },
  {
    text: "一点想法",
    value: "一点想法",
  },
  {
    text: "会议通知和活动通知",
    value: "会议通知和活动通知",
  },
  {
    text: "童话",
    value: "童话",
  },
  {
    text: "其他",
    value: "其他",
  },
];
// 年级
export const Grade = [
  {
    text: "大学后",
    value: "大学后",
  },
  {
    text: "高三",
    value: "高三",
  },
  {
    text: "高二",
    value: "高二",
  },
  {
    text: "高一",
    value: "高一",
  },
  {
    text: "初三",
    value: "初三",
  },
  {
    text: "初二",
    value: "初二",
  },
  {
    text: "初一",
    value: "初一",
  },
  {
    text: "六年级",
    value: "六年级",
  },
  {
    text: "五年级",
    value: "五年级",
  },
  {
    text: "四年级",
    value: "四年级",
  },
  {
    text: "三年级",
    value: "三年级",
  },
  {
    text: "二年级",
    value: "二年级",
  },
  {
    text: "一年级",
    value: "一年级",
  },
];

// 0自主，1作业，2测验
export const CompositionEvaluateSourcesMap = {
  0: "自主测评",
  1: "作业",
  2: "测验",
};

export const CompositionEvaluateSourcesColor = {
  0: "#3A66F5",
  1: "#F67518",
  2: "#07A069",
};
export const CompositionEvaluateSourcesOptions = transformRecordToOption(
  CompositionEvaluateSourcesMap,
  "number"
);
export const ScoreGradeMap = {
  一年级: 10,
  二年级: 20,
  三年级: 30,
  四年级: 30,
  五年级: 40,
  六年级: 40,
  七年级: 50,
  初一: 50,
  八年级: 50,
  初二: 50,
  九年级: 50,
  初三: 50,
  高一: 60,
  高二: 60,
  高三: 60,
};

export const ScoreEnum = {
  10: "10分（通常对应一年级）",
  20: "20分（通常对应二年级）",
  30: "30分（通常对应三四年级）",
  40: "40分（通常对应五六年级）",
  50: "50分（通常对应七八九年级）",
  60: "60分（通常对应高一到高三）",
};

export const ScoreEnumOptions = transformRecordToOption(ScoreEnum, "number");

export const WordCountMap = {
  一年级: 70,
  二年级: 150,
  三年级: 250,
  四年级: 350,
  五年级: 450,
  六年级: 550,
  七年级: 600,
  初一: 600,
  八年级: 600,
  初二: 600,
  九年级: 600,
  初三: 600,
  高一: 700,
  高二: 800,
  高三: 800,
};

/** 通过分数获取等级 */
export function getScoreLevel(score) {
  if (score >= 95) {
    return {
      levelLabel: "优上",
      level: "1",
      simpleLabel: "优",
      color: "#f0142f",
    };
  } else if (score >= 92) {
    return {
      levelLabel: "优",
      level: "1",
      simpleLabel: "优",
      color: "#f0142f",
    };
  } else if (score >= 90) {
    return {
      levelLabel: "优下",
      level: "1",
      simpleLabel: "优",
      color: "#f0142f",
    };
  } else if (score >= 87) {
    return {
      levelLabel: "良上",
      level: "2",
      simpleLabel: "良",
      color: "#f67518",
    };
  } else if (score >= 84) {
    return {
      levelLabel: "良",
      level: "2",
      simpleLabel: "良",
      color: "#f67518",
    };
  } else if (score >= 80) {
    return {
      levelLabel: "良下",
      level: "2",
      simpleLabel: "良",
      color: "#f67518",
    };
  } else if (score >= 77) {
    return {
      levelLabel: "中上",
      level: "3",
      simpleLabel: "中",
      color: "#f6ba03",
    };
  } else if (score >= 74) {
    return {
      levelLabel: "中",
      level: "3",
      simpleLabel: "中",
      color: "#f6ba03",
    };
  } else if (score >= 70) {
    return {
      levelLabel: "中下",
      level: "3",
      simpleLabel: "中",
      color: "#f6ba03",
    };
  } else if (score >= 66) {
    return {
      levelLabel: "需努力",
      level: "4",
      simpleLabel: "差",
      color: "#3a66f5",
    };
  } else if (score >= 60) {
    return {
      levelLabel: "应奋起",
      level: "4",
      simpleLabel: "差",
      color: "#3a66f5",
    };
  }
  return {
    levelLabel: "赶快追",
    level: "4",
    simpleLabel: "差",
    color: "#3a66f5",
  };
}
