<template>
  <view class="contents">
    <cu-custom bgColor="bg-green3" :isBack="true">
      <block slot="backText"></block>
      <block slot="content">充值管理</block>
    </cu-custom>

    <view class="margin-bottom-sm">
      <view class="cu-list menu-avatar">
        <view class="cu-item">
          <view
            class="cu-avatar round lg animation animation-shake"
            v-bind:style="{
              'background-image':
                'url(https://cdn.zhoukaiwen.com/mgzw_logo.jpg)',
            }"
          ></view>
          <view class="content flex-sub">
            <view class="text-black text-bold">{{ userInfo.realName }}</view>
            <view class="text-grey text-sm">
              <text>测评次数：{{ userInfo.evaluationNumber }}次</text>
              <text class="margin-left"
                >微课次数：{{ userInfo.microLessonsNumber }}次</text
              >
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 产品1 · 赞赏 -->
    <view class="bg-white padding-bottom-sm">
      <view class="skill-sequence-panel-content-wrapper bg-white">
        <scroll-view scroll-x="true" class="kite-classify-scroll">
          <view
            :class="[
              'kite-classify-cell',
              navShow == index ? 'kite-active' : '',
            ]"
            @click="itemBtn(index)"
            v-for="(item, index) in packageInfo"
            :key="index"
          >
            <view class="kite-tag">{{ packageType[item.type] }}</view>
            <view class="text-smdf mainColor text-bold margin-top-xs">
              {{ item.name }}
            </view>
            <view class="text-sm" style="margin-top: 8rpx; color: #636363">
              {{ item.type === "evaluation" ? "使用次数" : "观看次数" }}
              {{ item.giftCount }}
            </view>
            <view class="bottomP">
              <text class="text-df">￥</text>
              <text class="text-xl text-bold">{{ item.price }}</text>
              <!-- <view v-if="index == 0" style="font-size: 22rpx; color: #699cfb"
                >120次识别，全年有效</view
              > -->
            </view>
          </view>
        </scroll-view>
      </view>

      <view class="text-center">
        <text class="cuIcon-info" style="vertical-align: middle"></text>
        <text class="text-sm"
          >购买或使用遇到问题，请在个人中心/联系客服，联系我们</text
        >
      </view>

      <view class="padding-lr-lg padding-tb-sm">
        <view class="smBox">
          <!-- <image src="https://cdn.zhoukaiwen.com/zanshangIcon2.svg" mode="widthFix" style="width: 40rpx;vertical-align: middle; margin-right: 15rpx;"></image> -->
          <text class="text-bold text-sm" style="color: #07a069"
            >备注：用户也可访问测文网，使用网页进行识别批量测评哦~</text
          >
        </view>
      </view>

      <view class="title-header">
        <view class="title-text" style="font-family: z1 !important">
          手写识别及测评说明
        </view>
      </view>
      <view class="bg-white padding-lr padding-tb-sm">
        <view class="margin-bottom flex justify-start">
          <view
            class="listIcon margin-right-xs flex justify-center align-center"
          >
            <image
              src="https://cdn.zhoukaiwen.com/icon_test-js2.svg"
              mode="widthFix"
              style="width: 64rpx"
            ></image>
          </view>
          <view class="flex justify-around flex-direction padding-left-xs">
            <view class="text-bold text-df mainColor">手写识别</view>
            <view
              class="text-grey text-sm margin-top-xs"
              style="line-height: 1.5"
              >测文网OCR支持对手写文字进行拍照合并识别，用户可以通过拍照功能对手写文字进行识别，软件会自动识别手写体的文字，并将识别结果转换为可编辑的文本‌</view
            >
          </view>
        </view>
        <view class="margin-bottom flex justify-start">
          <view
            class="listIcon margin-right-xs flex justify-center align-center"
          >
            <image
              src="https://cdn.zhoukaiwen.com/icon_test-zk.svg"
              mode="widthFix"
              style="width: 72rpx"
            ></image>
          </view>
          <view class="flex justify-around flex-direction">
            <view class="text-bold text-df mainColor">作文测评</view>
            <view
              class="text-grey text-sm margin-top-xs"
              style="line-height: 1.5"
              >测文网作文测评，可针对作文进行全方位的评估，自动识别作文中的语法错误、拼写错误以及标点符号使用不当等问题，并提供实时纠正，此外，它还能对作文的内容进行深入分析，包括主题是否明确、结构是否合理、观点是否鲜明等，为写作者提供全面的写作指导‌，“智能分析、‌精准反馈‌”，提升学习效率</view
            >
          </view>
        </view>
      </view>

      <view class="title-header">
        <view class="title-text" style="font-family: z1 !important">
          作文微课说明
        </view>
      </view>
      <view class="bg-white padding-lr padding-tb-sm">
        <view class="margin-bottom flex justify-start">
          <view
            class="listIcon margin-right-xs flex justify-center align-center"
          >
            <image
              src="https://cdn.zhoukaiwen.com/icon_test-js2.svg"
              mode="widthFix"
              style="width: 64rpx"
            ></image>
          </view>
          <view class="flex justify-around flex-direction padding-left-xs">
            <view class="text-bold text-df mainColor">作文微课</view>
            <view
              class="text-grey text-sm margin-top-xs"
              style="line-height: 1.5"
              >视频微课提供全面覆盖各年级、精准匹配各类作文需求的视频课程，旨在让孩子们能够轻松便捷地在线学习，迅速掌握并提升写作技巧</view
            >
          </view>
        </view>
      </view>
      <view class="padding-left">
        <text
          class="cuIcon-info"
          style="vertical-align: middle; margin-right: 4rpx"
        ></text>
        <text class="text-sm"
          >微课视频不可二次售卖，视频享有版权保护，违者必究！</text
        >
      </view>
    </view>

    <view style="height: 250rpx; width: 1rpx"></view>

    <view class="bg-white padding bottomBox">
      <view class="text-center">
        <text
          class="cuIcon-info"
          style="vertical-align: middle; margin-right: 5rpx"
        ></text>
        <text class="text-sm"
          >支付成功后，您可返回小程序查看剩余次数，并使用完整功能</text
        >
      </view>

      <view class="blackBox flex justify-between">
        <view class="flex justify-center flex-direction padding-left-sm">
          <view
            class="text-bold text-white text-dflg"
            style="margin-bottom: 2rpx"
          >
            <text class="" style="color: #feddb0">支付</text>
            <text style="margin: 0 4rpx">{{ packageInfo[navShow].price }}</text>
            <text class="" style="color: #feddb0">元</text>
            <text class="margin-left-sm"
              >购买{{ packageType[packageInfo[navShow].type] }}</text
            >
          </view>
          <view class="text-xs text-grey" style="margin-top: 3rpx">
            <text>追梦的路上，测文网陪你勇敢前行</text>
          </view>
        </view>
        <view class="payBtn" @click="debouncedPay">去支付</view>
      </view>
    </view>
  </view>
</template>

<script>
import request from "@/common/request.js";
import { packageType, debounce } from "@/common/utils.js";
export default {
  data() {
    return {
      packageType,
      openId: "",

      modalName: null,
      nickname: "",
      phone: "",
      mailbox: "",

      navIndex: 0, // 0识别和测评 1作文微课
      navShow: 0, //选中的商品下标
      gifts: [
        {
          title: "识别与测评",
          tag: "购买120次",
          explain: "使用次数：120次/年",
          price: "398.00",
          businessType: "evaluationNumber",
          czNumbers: 120,
        },
        {
          title: "识别与测评",
          tag: "购买1次",
          explain: "使用次数：1次",
          price: "3.99",
          businessType: "evaluationNumber",
          czNumbers: 1,
        },
        {
          title: "识别与测评",
          tag: "购买3次",
          explain: "使用次数：3次",
          price: "10.00",
          businessType: "evaluationNumber",
          czNumbers: 3,
        },
        {
          title: "作文微课",
          tag: "购买1次",
          explain: "观看次数：1次",
          price: "9.9",
          businessType: "microLessonsNumber",
          czNumbers: 1,
        },
        {
          title: "作文微课",
          tag: "购买5次",
          explain: "观看次数：5次",
          price: "24.00",
          businessType: "microLessonsNumber",
          czNumbers: 5,
        },
      ],
      userInfo: {},
      packageInfo: [],
      debouncedPay: null,
    };
  },
  onShow() {},
  onLoad() {
    this.loginFun();
    this.getUserInfo();
    this.getPackage();
    this.debouncedPay = debounce(this.generateOrder);
  },
  methods: {
    loginFun() {
      var that = this;
      uni.login({
        provider: "weixin",
        success: (loginRes) => {
          console.log("loginRes", loginRes);
          that.LoginData(loginRes.code);
        },
      });
    },
    getPackage() {
      let opts = {
        url: "bse_package/mini/list",
        method: "GET",
      };
      request.httpTokenRequest(opts).then((res) => {
        this.packageInfo = res.data;
        console.log("packageInfo", this.packageInfo);
      });
    },
    getUserInfo() {
      let opts = {
        url: "auth/user_info",
        method: "GET",
      };
      request.httpTokenRequest(opts).then((res) => {
        const userInfo = res.data;
        console.log("userInfo", userInfo);
        if (userInfo.id) {
          this.userInfo = userInfo;
        }
      });
    },
    LoginData(code) {
      let opts = {
        url: "wechat/miniprogram/login",
        method: "POST",
      };
      request.httpTokenRequest(opts, { code }).then((res) => {
        this.openId = res.data.openid;
      });
    },
    // 获取支付前微信返回密钥等信息
    generateOrder() {
      uni.showLoading({
        title: "正在加载支付信息",
      });
      let opts = {
        url: "bse_payment_order/wechat/create",
        method: "POST",
      };
      let that = this;
      request
        .httpTokenRequest(opts, {
          userId: that.userInfo.id,
          packageId: that.packageInfo[that.navShow].id,
          packageSnapshot: JSON.stringify(that.packageInfo[that.navShow]),
          openid: that.openId,
          amountPayable: that.packageInfo[that.navShow].price * 100,
          body: `购买了${
            packageType[that.packageInfo[that.navShow].type]
          },次数增加了${that.packageInfo[that.navShow].giftCount}次`,
        })
        .then((res) => {
          that.goPay(res.data.payParams);
          uni.hideLoading();
        })
        .finally(() => {
          uni.hideLoading();
        });
    },
    // 去支付
    goPay(param) {
      var that = this;
      wx.requestPayment({
        timeStamp: param.timeStamp,
        nonceStr: param.nonceStr,
        package: param.package,
        signType: param.signType,
        paySign: param.paySign,
        success: function (res) {
          // success
          wx.showToast({
            title: "支付成功",
            icon: "success",
            duration: 2000,
          });
          that.addNum();
        },
        fail: function (res) {
          // fail
          wx.showToast({
            title: "支付失败",
            icon: "success",
            duration: 2000,
          });
        },
        complete: function () {
          // complete
        },
      });
    },
    // 支付成功后，系统增加次数（微课、测评）
    addNum() {
      var that = this;
      let opts = {
        url: "sys_user/updateTimes",
        method: "POST",
      };
      let params = {
        id: that.userInfo.id,
        type: "add",
        rowType:
          that.packageInfo[that.navShow].type === "evaluation"
            ? "evaluationNumber"
            : "microLessonsNumber", //微课或测评
        numbers: that.packageInfo[that.navShow].giftCount, //充值次数
      };
      request.httpTokenRequest(opts, params).then((res) => {
        this.getUserInfo();
        wx.showToast({
          title: "充值成功",
          icon: "success",
          duration: 2000,
        });
      });
    },
    hideModal(e) {
      this.modalName = null;
    },
    navBtn(index) {
      this.navIndex = index;
      this.navShow = 0;
    },
    itemBtn(index) {
      this.navShow = index;
    },
  },
};
</script>

<style lang="scss" scoped>
// 主要颜色：#2832CD
// 渐变：#699CFB
// 字体/标签颜色：#2E3551

// 按钮颜色1：#3475FB
// 按钮颜色2：#0204AF

/*scroll-view外层*/
.skill-sequence-panel-content-wrapper {
  width: 100%;
  position: relative;
  white-space: nowrap;
  padding: 10rpx 0rpx;
  border-radius: 20rpx;
}
.kite-classify-cell-first {
  display: inline-block;
  width: 290rpx;
  height: 220rpx;
  margin: 25rpx 25rpx 15rpx 25rpx;
  padding: 20rpx;
  position: relative;
  border-radius: 20rpx 70rpx 20rpx 20rpx;
  border: 6rpx solid transparent;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image: linear-gradient(to right, #f9f9f9, #f9f9f9),
    linear-gradient(115deg, #e6e6e6, #e6e6e6);
}

.kite-classify-cell {
  display: inline-block;
  width: 210rpx;
  height: 220rpx;
  margin: 25rpx 25rpx 15rpx 25rpx;
  padding: 20rpx;
  position: relative;
  border-radius: 20rpx 60rpx 20rpx 20rpx;
  border: 6rpx solid transparent;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image: linear-gradient(to right, #f9f9f9, #f9f9f9),
    linear-gradient(115deg, #e6e6e6, #e6e6e6);
}
.kite-active {
  background-image: linear-gradient(to right, #dcfeec, #dcfeec),
    linear-gradient(115deg, #25b67d, #00b67d) !important;
}
.kite-tag {
  padding: 5rpx 12rpx;
  background-color: #2e3551;
  color: #ffffff;
  position: absolute;
  top: -20rpx;
  left: 12rpx;
  font-size: 20rpx;
  border-radius: 6rpx;
}
.bottomP {
  position: absolute;
  bottom: 20rpx;
  left: 15rpx;
  color: #00b67d;
}
.mainColor {
  color: #2e3551;
}
.smBox {
  width: 100%;
  padding: 14rpx 20rpx;
  // min-height: 60rpx;
  margin: 0 auto;
  background-image: linear-gradient(to right, #e3f1f3, #d4e5f7);
  border-radius: 12rpx;
}

.bottomBox {
  width: 750rpx;
  min-height: 150rpx;
  position: fixed;
  bottom: 0;
  z-index: 1024;
  box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.1);
  .blackBox {
    width: 690rpx;
    height: 95rpx;
    margin: 25rpx auto;
    border-radius: 20rpx;
    background-color: #242529;
    overflow: hidden;
    .payBtn {
      width: 170rpx;
      height: 95rpx;
      line-height: 95rpx;
      border-radius: 20rpx;
      color: #ffffff;
      text-align: center;
      font-size: 30rpx;
      background-image: linear-gradient(145deg, #25b67d, #00b67d);
    }
  }
}
.title-header {
  display: flex;
  height: 115rpx;
  font-size: 36rpx;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  background-image: url(https://cdn.zhoukaiwen.com/wccswF.png);
  background-size: cover;
  .title-text {
    background-image: -webkit-linear-gradient(0deg, #25b67d, #00b67d);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
.listIcon {
  width: 75rpx;
  height: 75rpx;
  border-radius: 20rpx;
  background-image: linear-gradient(145deg, #e3f1f3, #d4e5f7);
}
.bg-blue2 {
  background-color: #3475fb;
  color: #ffffff;
}
.bg-blueTc {
  background-image: linear-gradient(145deg, #25b67d, #07a069);
  color: #ffffff;
}

.cu-dialog {
  background: #ffffff;
  overflow: visible;
  padding: 240rpx 0 50rpx 0;
  .modal_bg {
    width: 100%;
    height: 320rpx;
    position: absolute;
    top: -100rpx;
    background-image: url(https://zhoukaiwen.com/img/qdpz/modal_bg.png);
    background-size: 100%;
    background-repeat: no-repeat;
  }
  .modal_main {
    width: 600rpx;
    margin: 0 auto;
    background-color: #ffffff;
  }
  .cu-form-group {
    min-height: 90rpx !important;
  }
  .cu-form-group input {
    padding-left: 10rpx;
  }
}
</style>
