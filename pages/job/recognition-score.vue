<template>
  <view class="component" style="position: relative">
    <view
      class="topBg"
      style="background-image: url(https://cdn.zhoukaiwen.com/cewen_me_bg.png)"
    >
      <view class="cu-custom">
        <view
          class="fixed"
          :style="style"
          style="justify-content: center; align-content: center"
        >
          <view
            class="text-white text-bold text-lg text-center"
            style="position: relative"
            :style="[{ top: StatusBar + 'px' }]"
          >
            <text
              @click="goBack"
              class="cuIcon-back text-left"
              style="position: absolute; left: 0; top: 6rpx"
            ></text>
            <text class="">修改作文</text>
          </view>
        </view>
      </view>
    </view>

    <view class="mainBox" :style="[{ top: CustomBar + 10 + 'px' }]">
      <view class="listBox bg-white shadow">
        <view class="flex justify-between align-center">
          <view class="text-green3">
            <text class="cuIcon-titles"></text>
            <text class="text-black text-bold">作文信息</text>
          </view>
          <!-- <view class="text-sm">收起</view> -->
        </view>
        <view
          class="flex justify-between padding-lr-xs margin-top-lg padding-bottom-sm text-black solid-bottom"
        >
          <view>测评年级</view>
          <view>{{ compositionInfo.grade }}</view>
        </view>
        <view 
        class="flex justify-between padding-lr-xs margin-top-lg padding-bottom-sm text-black solid-bottom"
        
        >
					<view>
						作者
					</view>
					<input class="text-right" v-model="compositionInfo.author" placeholder="请填写作者姓名" name="input"></input>
				</view>

        <view
          class="flex justify-between padding-lr-xs margin-top padding-bottom-sm text-black solid-bottom"
        >
          <view>作文文体</view>
          <view>{{ compositionInfo.wenTi || compositionInfo.requireWenTi}}</view>
        </view>
        <view
          class="flex justify-between padding-lr-xs margin-top padding-bottom-sm text-black solid-bottom"
        >
          <view>作文单元</view>
          <form-select
            placeholder="作文单元"
            v-model:value="compositionInfo.did"
            :dataList="unitList"
            :text="'dname'"
            :disabled="compositionInfo.evaluateSources==1"
            :name="'did'"
          />
        </view>
        <view
          class="flex justify-between padding-lr-xs margin-top padding-bottom-sm text-black solid-bottom"
        >
          <view>字数要求</view>
          <view>{{ compositionInfo.requireWords!=0? '本篇写作不少于 '+compositionInfo.requireWords+' 字' : '暂无要求' }}</view>
        </view>
        <view
          class="flex justify-between padding-lr-xs margin-top padding-bottom-sm text-black solid-bottom"
        >
          <view>写作要求</view>
					<input class="text-right" v-model="compositionInfo.requireTitle" placeholder="暂无要求" name="input"></input>
        </view>
      </view>

      <view class="listBox bg-white shadow">
        <view class="flex justify-between align-center">
          <view class="text-green3">
            <text class="cuIcon-titles"></text>
            <text class="text-black text-bold">修改区</text>
          </view>
          <view class="text-sm text-green3" v-if="compositionInfo.initLogId" @click="toHistory">
            <text style="margin-right: 4rpx">查看原文</text>
            <text class="cuIcon-right"></text>
          </view>
        </view>

        <view class="cu-list menu sm-border bg-white">
          <!-- <view class="padding-lr padding-tb-sm">
						<view class="flex justify-between">
							<view class="text-lg text-black text-bold">{{compositionInfo.title}}</view>
							<view class="action">
								<text>{{ compositionInfo.requireWenTi }}</text>
							</view>
						</view>
						<view class="margin-bottom-sm text-gray margin-top-xs">
							{{ compositionInfo.author }}
						</view>
						<view v-if="state != 1" style="width: 100%">
							<image style="display: block; margin: 0 auto; width: 100%" :src="compositionInfo.imgUrl"
								mode="widthFix" />
						</view>
					</view> -->

          <!-- 文章编辑区 -->
          <view
            class="cu-bar justify-left bg-white solid-bottom"
            style="position: relative"
          >
            <input
              class="text-black text-lg text-bold"
              style="width: 100%; text-align: center"
              v-model="compositionInfo.title"
              placeholder="请填写作文标题"
              >
            >
            <view
              v-if="compositionInfo.content"
              style="position: absolute; right: 0;z-index: 100;"
            >
              <TtsPlayer :message="compositionInfo.content" />
            </view>
          </view>
          <view class="solid-bottom">
            <textarea
              class="text-lg padding-lr padding-tb bg-white"
              style="
                width: 100%;
                height: 500rpx;
                letter-spacing: 2rpx;
                white-space: pre-line;
              "
              v-model="compositionInfo.content"
              maxlength="999999999"
              placeholder="上传图片自动识别"
            />
          </view>
        </view>
      </view>
      <view class="safe-area-inset-bottom" style="height: 125rpx"></view>
    </view>

    <!-- 底部操作栏 -->

    <view class="cu-bar bg-white tabbar border shop bottomBox solid-top">
      <button class="action" @click="toAiChat()">
        <view class="cuIcon-service text-green3">
          <view class="cu-tag badge"></view>
        </view>
        在线辅导
      </button>
      <view class="btn-group">
        <button
          @click="handleEvaluate()"
          style="width: 80%; height: 70rpx"
          :disabled="isDisable"
          class="cu-btn bg-green3 round shadow-blur"
        >
          {{ "批阅/评分" }}
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import request from "@/common/request.js";
import TtsPlayer from "@/components/TtsPlayer.vue";
import { parseResult } from "@/common/utils.js";
import formSelect from "@/components/form-select.vue";
export default {
  components: {
    TtsPlayer,
    formSelect,
  },
  data() {
    return {
      CustomBar: this.CustomBar,
      modalName: null,
      // 基础信息
      type: "1", //1 作业入口测评  2 自主测评
      currentTab: 0,
      unitList: [],

      // 作文信息
      compositionInfo: {
        compositionId: "",
        compositionPath: "",
        source: "小程序",
        title: "",
        requireTitle: "",
        wenTi: "",
        requireWenTi: "",
        words: "",
        requireWords: "",
        content: "",
        author: "",
        grade: "",
        sensitive: "",
        score: "",
        percentage: "",
        fullScore: "",
        classified: "",
        paragraphs: "",
        sentences: "",
        advice: "",
        comments: "",
        evaluateSources: 0,
        answerStatus: 0,
        initLogId: "",
        compositionPath: "",
        star: "",
        did: "0",
      },

      // 评分数据
      evaluationData: {},
      // 用户信息
      userInfo: {},
      compositionAnswerId: null,
      isDisable: false,
    };
  },
  computed: {
    style() {
      const StatusBar = this.StatusBar;
      const CustomBar = this.CustomBar;
      const bgImage = this.bgImage;
      const style = `height:${CustomBar}px;padding-top:${StatusBar}px;`;
      if (this.bgImage) {
        style = `${style}background-image:url(${bgImage});`;
      }
      return style;
    },
  },

  onLoad(option) {
    // 0自主，1作业，2测验
    if(option.id){
      console.log(option)
      this.getCompositionInfo(option.id)
    }else{
      const evaluateSources = Number(option.evaluateSources || 0);
      this.compositionInfo.wenTi = option.wenTi;
      this.compositionInfo.title = option.title;
      this.compositionInfo.author = option.author;
      this.compositionInfo.compositionPath = option.compositionPath || "";
      this.compositionInfo.grade = option.grade;
      this.compositionInfo.evaluateSources = evaluateSources;
      this.compositionInfo.requireWords = option.requireWords || "0";
      this.compositionInfo.fullScore = option.fullScore || "0";
      this.compositionInfo.requireWenTi = option.requireWenTi || "";
      this.compositionInfo.requireTitle = option.requireTitle||option.compositionRequireTitle || "";
      this.compositionInfo.did = option.did || "0";
      this.compositionInfo.initLogId = option.initLogId || "";
      this.compositionInfo.compositionId = option.compositionId || undefined;
      this.processImage(option.imgUrl);
      this.getUserInfo();
      this.getUnitList(this.compositionInfo.grade);
    }
    
  },

  methods: {
    getCompositionInfo(id){
      request.httpTokenRequest({
        url: "biz_composition_log/" + id,
        method: "GET",
      }).then((res) => {
        const option = res.data;
        this.compositionInfo.wenTi = option.wenTi;
        this.compositionInfo.title = option.title;
        this.compositionInfo.author = option.author;
        this.compositionInfo.compositionPath = option.compositionPath || "";
        this.compositionInfo.grade = option.grade;
        this.compositionInfo.evaluateSources = option.evaluateSources;
        this.compositionInfo.requireWords = option.requireWords || "0";
        this.compositionInfo.fullScore = option.fullScore || "0";
        this.compositionInfo.requireWenTi = option.requireWenTi || "";
        this.compositionInfo.requireTitle = option.requireTitle||option.compositionRequireTitle || "";
        this.compositionInfo.initLogId = option.initLogId || "";
        this.compositionInfo.did ="0";
        this.compositionInfo.compositionId = option.compositionId || undefined;
        this.compositionInfo.content = option.ContentList ? `\t${option.ContentList?.map(e => e.Ccontent).join('\n\t')}` : ``;;
      this.getUnitList(this.compositionInfo.grade);

      });
    },
    toHistory(){
      uni.navigateTo({
        url: `/pages/me/historyDetail?id=${this.compositionInfo.initLogId}`,
      });
    },
    showImage() {
      const paths=this.compositionInfo.compositionPath.split(',')
      uni.previewImage({
        urls: paths,
        current: 0,
      });
    },
    toAiChat() {
      uni.navigateTo({
        url:
          "/pages/job/ai-chat?compositionId=" +
          this.compositionInfo.compositionId +
          "&title=" +
          this.compositionInfo.title +
          "&requireTitle=" +
          this.compositionInfo.requireTitle,
      });
    },
    getUnitList(grade) {
      let opts = {
        url: "bse_danyuan/list",
        method: "POST",
      };
      request.httpTokenRequest(opts, { dgrade: grade }).then((res) => {
        const data = res.data || [];
        this.unitList = data.map((item) => {
          return {
            did: item.id,
            dname: `${item.dgrade}${item.ddanyuan}${item.dname}`,
          };
        });
        this.unitList.unshift({
          did: "0",
          dname: "非单元作文",
        });
      });
    },
    getUserInfo() {
      this.userInfo = uni.getStorageSync("userInfo");
    },
    // 图片处理
    async processImage(imgUrl) {
      uni.showLoading({
        title: "正在识别中...",
      });
      try {
        const base64 = await this.imageToBase64(imgUrl);
        this.compositionInfo.imgUrl = `data:image/jpeg;base64,${base64}`;
        await this.performOCR(base64);
      } catch (error) {
        console.error("图片处理失败:", error);
        uni.showToast({
          title: "图片处理失败",
          icon: "none",
        });
      } finally {
        uni.hideLoading();
      }
    },

    // 图片转Base64
    imageToBase64(url) {
      return new Promise((resolve, reject) => {
        uni.getFileSystemManager().readFile({
          filePath: url,
          encoding: "base64",
          success: (res) => resolve(res.data),
          fail: reject,
        });
      });
    },

    // OCR识别
    async performOCR(base64) {
      try {
        const res = await request.httpTokenRequest(
          {
            url: "proxy/ocr",
            method: "post",
          },
          {
            ImgBase64: base64,
            proxyCode: "cww",
          }
        );

        const data = parseResult(res.data);
        this.compositionInfo.content = data.content || "";
        // this.compositionInfo.title = data.title || "";
      } catch (error) {
        console.error("OCR识别失败:", error);
        uni.showToast({
          title: "OCR识别解析失败",
          icon: "none",
        });
      }
    },
    // 评分相关方法
    async handleEvaluate() {
      // const hasEnoughTimes = await this.checkEvaluationTimes();
      // if (!hasEnoughTimes) {
      //   uni.showToast({
      //     title: "次数不足，请先充值！",
      //     duration: 1300,
      //     icon: "none",
      //   });
      //   return;
      // }
      await this.performEvaluation();
    },

    // 检查评分次数
    async checkEvaluationTimes() {
      try {
        const res = await request.httpTokenRequest(
          {
            url: "sys_user/updateTimes",
            method: "post",
          },
          {
            id: this.userInfo.id,
            rowType: "evaluationNumber",
            type: "sub",
          }
        );
        this.userInfo.evaluationNumber = res.data;
        uni.setStorageSync("userInfo", this.userInfo);
        return res.data >= 0;
      } catch (error) {
        console.error("检查评分次数失败:", error);
        return false;
      }
    },

    // 执行评分
    async performEvaluation() {
      uni.showLoading({
        title: "正在评分中...",
      });
      this.isDisable = true;
      try {
        const res = await request.httpTokenRequest(
          {
            url: "proxy/ceping",
            method: "POST",
          },
          {
            get_title: this.compositionInfo.title,
            get_content: this.compositionInfo.content,
            get_nianji: this.compositionInfo.grade,
            get_wenti: this.compositionInfo.wenTi,
            get_yqzs: this.compositionInfo.requireWords || "0",
            get_yqwenti:
              this.compositionInfo.requireWenTi ||
              this.compositionInfo.wenTi ||
              "",
            get_Sfullmarks: this.compositionInfo.fullScore || "0",
            get_yqtitle: this.compositionInfo.requireTitle || "",
            get_zxsx: this.compositionInfo.did || "0",
          }
        );
        const data = parseResult(res?.data);
        if (data.result === "0") {
          this.evaluationData = data;
          this.compositionInfo.wenTi = this.evaluationData.Style;
          this.compositionInfo.requireWenTi = this.evaluationData.StyleLimit;
          this.compositionInfo.words = this.evaluationData.Wordcount;
          this.compositionInfo.requireWords =
            this.evaluationData.WordCountLimit;
          this.compositionInfo.sensitive = this.evaluationData.sensitive;
          this.compositionInfo.score = this.evaluationData.score;
          this.compositionInfo.percentage = this.evaluationData.Percentage;
          this.compositionInfo.fullScore = this.evaluationData.Sfullmarks;
          this.compositionInfo.classified = this.evaluationData.Classified;
          this.compositionInfo.paragraphs = this.evaluationData.Paragraphcount;
          this.compositionInfo.sentences = this.evaluationData.Sentencecount;
          this.compositionInfo.advice = this.evaluationData.Advice;
          this.compositionInfo.comments = this.evaluationData.Comments;
          this.compositionInfo.answerStatus = 0;
          this.compositionInfo.star = this.evaluationData.Star;
          await this.saveCompositionLog();
        } else {
          uni.showToast({
            title: data.errmsg,
            icon: "none",
          });
        }
      } catch (error) {
        console.error("评分失败:", error);
        uni.showToast({
          title: "评分失败",
          icon: "none",
        });
        this.isDisable = false;
      }
    },
    // 保存测评记录
    async saveCompositionLog() {
      try {
        const res = await request.httpTokenRequest(
          {
            url: "biz_composition_log/",
            method: "POST",
          },
          {
            ...this.evaluationData,
            ...this.compositionInfo,
            compositionId: this.compositionInfo.compositionId || undefined,
            initLogId: this.compositionInfo.initLogId || undefined,
          }
        );
        uni.navigateTo({
          url: `/pages/me/historyDetail?id=${res.data}`,
        });
      } catch (error) {
        console.error("保存测评记录失败:", error);
      } finally {
        uni.hideLoading();
        this.isDisable = false;
      }
    },

    // 提交作文
    async submitComposition() {
      try {
        await request.httpTokenRequest(
          {
            url: "biz_composition_log/",
            method: "PUT",
          },
          {
            id: this.compositionAnswerId,
            ...this.compositionInfo,
            answerStatus: 10,
          }
        );

        uni.showToast({
          title: "提交成功！",
          duration: 1300,
        });
        setTimeout(
          () =>
            uni.navigateBack({
              delta: 1,
            }),
          1500
        );
      } catch (error) {
        console.error("提交作文失败:", error);
        uni.showToast({
          title: "提交失败",
          icon: "none",
        });
      }
    },
    goBack() {
      uni.navigateBack({
        delta: 1,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.topBg {
  background-size: 100% 100%;
  /* background-size: cover; */
  height: 760rpx;
  width: 750rpx;
  overflow: hidden;
  flex-direction: column;
  padding: 20rpx;
  position: absolute;
  top: 0;
  left: 0;
}

.mainBox {
  width: 700rpx;
  position: absolute;
  left: 25rpx;
}

.listBox {
  padding: 25rpx 30rpx;
  border-radius: 15rpx;
  margin: 25rpx 0;
}

.bottomBox {
  width: 750rpx;
  position: fixed;
  left: 0;
  bottom: 0rpx;
  z-index: 100;
}

.centre {
  text-align: center;
  margin: 10rpx auto 50rpx;
  font-size: 32rpx;

  image {
    width: 300rpx;
    border-radius: 50%;
    margin: 0 auto;
  }

  .tips {
    font-size: 24rpx;
    color: #999999;
    margin-top: 20rpx;
  }
}

.button-group {
  padding: 20rpx 30rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  gap: 20rpx; // 按钮之间的间距

  .cu-btn {
    flex: 1; // 按钮等宽
    margin: 0; // 移除默认边距
  }
}

// 为了防止底部内容被固定按钮遮挡
.container {
  padding-bottom: 180rpx;
}
</style>
