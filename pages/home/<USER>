<!-- 首页 -->
<template>
  <view class="components-home" style="position: relative">
    <view
      class="topBg"
      style="background-image: url(https://cdn.zhoukaiwen.com/cewen_me_bg.png)"
    >
      <view class="cu-custom">
        <view
          class="fixed text-center"
          :style="style"
          style="justify-content: center"
        >
          <view
            class="text-white text-bold text-lg"
            :style="[{ top: StatusBar + 'px' }]"
          >
            作文微课
          </view>
        </view>
      </view>
    </view>

    <!-- 列表主区域 -->
    <view class="mainBox" :style="[{ top: CustomBar + 'px' }]">
      <!-- 视频列表 -->
      <view
        style="border-radius: 20rpx"
        class="cu-card article card bg-white"
        v-if="listData.length > 0"
      >
        <view
          class="cu-item shadow padding-top solid-bottom"
          v-for="(item, index) in listData"
          :key="index"
        >
          <view class="content">
            <image
              :src="
                item.Vimg ||
                'https://ossweb-img.qq.com/images/lol/web201310/skin/big10006.jpg'
              "
              mode="aspectFill"
            ></image>
            <view class="desc">
              <view class="text-bold">{{ item.Title || "视频标题" }}</view>
              <view class="text-sm">
                <text class="text-gray">适合年级：</text>
                <text class="text-grey">{{ item.Grade || "初中段" }}</text>
              </view>
              <view class="flex justify-between">
                <view class="cu-tag bg-green light sm radius">{{
                  item.Scorepoints || "开篇押题"
                }}</view>
                <!-- @tap.stop="item.IsShopping ? watchVideo(item) : buyVideo(item)" -->
                <button
                  @tap.stop="
                    buyList.find((buyItem) => buyItem.businessId == item.Id)
                      ? watchVideo(item)
                      : buyWk(item)
                  "
                  class="cu-btn radius sm bg-green3 light flex align-center text-df"
                  style="margin-top: -12rpx; font-size: 23rpx !important"
                >
                  {{
                    buyList.find((buyItem) => buyItem.businessId == item.Id)
                      ? "立即观看"
                      : "购买观看"
                  }}
                  <text class="cuIcon-right"></text>
                </button>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 暂无数据 -->
      <view class="page-box" v-if="listData.length === 0 && !loading">
        <view>
          <view class="centre">
            <image
              src="https://cdn.zhoukaiwen.com/noData1.png"
              mode="widthFix"
            ></image>
            <view class="explain">
              暂无微课数据
              <view class="tips">请稍后再试或联系管理员</view>
            </view>
          </view>
        </view>
      </view>

      <view style="height: 160rpx; width: 1rpx"></view>
    </view>
  </view>
</template>

<script>
import request from "@/common/request.js";
export default {
  data() {
    return {
      CustomBar: this.CustomBar,
      listData: [],
      loading: false,
      buyList: [],
    };
  },
  computed: {
    style() {
      const StatusBar = this.StatusBar;
      const CustomBar = this.CustomBar;
      const bgImage = this.bgImage;
      const style = `height:${CustomBar}px;padding-top:${StatusBar}px;`;
      if (this.bgImage) {
        style = `${style}background-image:url(${bgImage});`;
      }
      return style;
    },
  },
  mounted() {
    this.getVideoList();
    this.getUserInfo();
  },
  methods: {
    // 数组比较
    processArrays() {
      // 模拟数据测试
      // let listData = [
      //   { id: 1 },
      //   { id: 2 },
      //   { id: 3 },
      //   { id: 4 },
      //   { id: 5 }
      // ];
      // let buyList = [
      //   { id: 2, timeStamp: '1743009853' },
      //   { id: 3, timeStamp: '1743010453' },
      //   { id: 4, timeStamp: '1742923453' }
      // ];
      // 获取当前时间戳（秒）
      const currentTimestamp = Math.floor(Date.now() / 1000);
      // 24小时的秒数
      const twentyFourHoursInSeconds = 24 * 60 * 60;

      // 更新 listData
      const updatedListData = this.listData.map((item) => {
        // 检查当前 item.id 是否在 buyList 中
        const boughtItem = this.buyList.find(
          (buyItem) => buyItem.businessId == item.Id
        );

        if (boughtItem) {
          // 计算时间差（秒）
          const timeDiff =
            currentTimestamp - parseInt(boughtItem.operationTime);

          return {
            ...item,
            isBuy: true, // 匹配到 buyList，表示已购买
            timer: timeDiff <= twentyFourHoursInSeconds, // 未超过24小时为 true，否则 false
          };
        } else {
          // 未匹配到 buyList，表示未购买
          return {
            ...item,
            isBuy: false,
            timer: null, // 未购买，timer 为 null
          };
        }
      });

      // 更新后的数据
      console.log(updatedListData);
      // 如果是在 Vue 中，可以 this.listData = updatedListData;
    },
    getUserInfo() {
      let opts = {
        url: "auth/user_info",
        method: "GET",
      };
      request.httpTokenRequest(opts).then((res) => {
        const userInfo = res.data;
        if (userInfo.id) {
          this.userInfo = userInfo;
          this.getBuyList();
        }
      });
    },
    // 获取视频列表
    getVideoList() {
      this.loading = true;
      uni.showLoading({
        title: "加载中",
      });

      // 使用完整请求格式
      uni.request({
        url: "https://api.cewenwang.com/api/VideoList/38",
        method: "GET",
        header: {
          "Content-Type": "application/json",
        },
        success: (res) => {
          console.log(res);
          this.listData = res.data || [];
          this.loading = false;
          uni.hideLoading();
          // this.processArrays();
        },
        fail: () => {
          this.loading = false;
          uni.hideLoading();
          uni.showToast({
            title: "加载失败",
            icon: "none",
          });
        },
      });
    },

    // 前往视频详情页
    goVideoDetail(item) {
      uni.navigateTo({
        url: `/pages/home/<USER>
      });
    },

    // 购买并观看视频
    buyVideo(item) {
      // 阻止事件冒泡
      event.stopPropagation();

      // 可以在这里增加购买逻辑
    },
    // 直接观看视频（已购买）
    watchVideo(item) {
      // 直接跳转到详情页
      this.goVideoDetail(item);
    },
    // 旧方法保留（可能有其他地方调用）
    goVideo() {
      uni.navigateTo({
        url: "/pages/user/video",
      });
    },
    // 购买微课，// 宇杰检查：检查接口参数，购买微课传值内容，我测试购买没有购买记录；
    buyWk(item) {
      var that = this;
      let opts = {
        url: "sys_user/updateTimes",
        method: "POST",
      };
      let params = {
        id: that.userInfo.id,
        type: "sub",
        rowType: "microLessonsNumber", //消费：微课
        numbers: 1, //充值次数
        businessId: item.Id, //微课ID
      };
      uni.showModal({
        title: "提示",
        content: `是否购买《${item.Title}》?`,
        success: (res) => {
          if (res.confirm) {
            request.httpTokenRequest(opts, params).then((res) => {
              if (res.data >= 0) {
                this.goVideoDetail(item);
                this.getBuyList();
              } else {
                uni.showToast({
                  title: "次数不足,请先购买/充值",
                  icon: "none",
                });
              }
            });
          }
        },
      });
    },
    // 查询购买列表
    getBuyList() {
      let opts = {
        url: "bse_user_number_record/user_list",
        method: "GET",
      };
      let params = {
        userId: this.userInfo.id,
        businessType: "microLessonsNumber", //微课
      };
      request.httpTokenRequest(opts, params).then((res) => {
        this.buyList = res.data;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.topBg {
  background-size: 100% 100%;
  /* background-size: cover; */
  height: 760rpx;
  width: 750rpx;
  overflow: hidden;
  flex-direction: column;
  padding: 20rpx;
  position: absolute;
  top: -20rpx;
  left: 0;
}
.mainBox {
  width: 700rpx;
  position: absolute;
  left: 25rpx;
}
.page-box {
  padding: 100rpx 0;

  .centre {
    text-align: center;

    image {
      width: 280rpx;
      margin-bottom: 20rpx;
    }

    .explain {
      font-size: 30rpx;
      color: #999;

      .tips {
        font-size: 24rpx;
        color: #ccc;
        margin-top: 10rpx;
      }
    }
  }
}

.loading-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .loading-text {
    margin-top: 20rpx;
    color: #999;
    font-size: 28rpx;
  }
}
.cu-card > .cu-item {
  margin: 0 !important;
}
</style>
