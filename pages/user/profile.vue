<!-- 我的资料 -->
<template>
	<view class="container">
		<cu-custom bgColor="bg-green3" :isBack="true">
			<block slot="backText"></block>
			<block slot="content">我的资料</block>
		</cu-custom>
		<view class="carBox">
			<view class="cu-form-group">
				<view class="title text-black">姓名</view>
				<input class="text-right" v-model="form.nickName" placeholder="请输入您的姓名" name="input"></input>
			</view>
			<view class="cu-form-group">
				<view class="title text-black">性别</view>
				<form-select
            placeholder="性别"
            v-model:value="form.gender"
            :dataList="picker_gender"
            :text="'name'"
            :disabled="true"
            :name="'id'"
          />
			</view>
			
			
			<view class="cu-form-group margin-top-sm">
				<view class="title text-black">学校</view>
				<view class="picker" style="color: #555;">
						{{form.schoolName||'请选择'}}
				</view>
			</view>
			
			<view class="cu-form-group" v-if="['4','5'].includes(form.userType)">
				<view class="title text-black">年级/班级</view>
				<!-- <picker @change="PickerChange_nj" :value="index_nj" :range="picker_nj.map(e=>e.name)"> -->
					<view class="picker" style="color: #555;">
						{{form.gradeName+''+form.clazzName||'请选择'}}
					</view>
				<!-- </picker> -->
			</view>
			
      <view class="cu-form-group">
				<view class="title text-black">学号</view>
        {{ form.studentNum }}
			</view>
			
			<view class="cu-form-group margin-top-sm">
				<view class="title text-black">电话</view>
        {{ form.phone }}
			</view>
			
			<view class="cu-form-group">
				<view class="title text-black">邮箱</view>
        {{ form.email }}
			</view>
		</view>
		
	</view>
</template>

<script>
import request from "@/common/request.js";
import searchCombox from "@/components/search-combox.vue";
import formSelect from "@/components/form-select.vue";
export default {
  components: {
    searchCombox,
    formSelect,
  },
  data() {
    return {
      openId: "",
      nickName: "",
      gender: "",
      phone: "",
      email: "",
      schoolCode: "",
      gradeCode: "",
      class: "",

      index_gender: -1,
      show_gender: false,
      picker_gender: [
        {
          id: "0",
          name: "保密",
        },
        {
          id: "1",
          name: "男",
        },
        {
          id: "2",
          name: "女",
        },
      ],

      index_nj: -1,
      index_class: -1,
      picker_nj: [],
      picker_class: [],
      schoolCodeList: [],

      tongyi: 0,
      form: {
        userType: 0,
        schoolCode: "",
        schoolName: "",
        gradeCode: "",
        gradeName: "",
        clazzCode: "",
        clazzName: "",
        gender: "",
        email: "",
        phone: "",
        nickName: "",
        realName: "",
        studentNum: "",
      },
    };
  },
  onLoad(option) {
    var that = this;
    // uni.login({
    //   provider: "weixin",
    //   success: (loginRes) => {
    //     console.log(loginRes);
    //     that.wxLogin(loginRes.code);
    //   },
    // });
    this.getUserInfo();
    this.getschoolCodeList();
  },
  methods: {
    checkboxChange(e) {
      console.log(e.detail.value);
      if (e.detail.value.length < 1) {
        this.tongyi = 0;
      } else {
        this.tongyi = 1;
      }
      console.log(this.tongyi);
    },
    goYhxy() {
      uni.navigateTo({
        url: "/pages/me/yhxy",
      });
    },
    goYsxy() {
      uni.navigateTo({
        url: "/pages/me/ysxy",
      });
    },
    // wxLogin(code) {
    //   let opts = {
    //     url: "wx/getAccessToken",
    //     method: "POST",
    //   };
    //   request.httpRequest(opts, { code: code }).then((res) => {
    //     const data = JSON.parse(res.data);
    //     this.openId = data.openid;
    //     this.getUserInfo(data);
    //   });
    // },
    getUserInfo() {
      let opts = {
        url: "auth/user_info",
        method: "GET",
      };
      request.httpTokenRequest(opts).then((res) => {
        const userInfo = res.data;
        if (userInfo.id) {
          this.form.nickName = userInfo.nickName;
          this.form.gender = userInfo.gender;
          this.index_gender = this.picker_gender.findIndex(
            (e) => e.id == this.form.gender
          );

          this.form.phone = userInfo.phone;
          this.form.email = userInfo.email;
          this.form.schoolCode = userInfo.schoolCode;
          this.form.schoolName = userInfo.schoolName;
          this.form.gradeCode = userInfo.gradeCode;
          this.form.gradeName = userInfo.gradeName;
          this.form.clazzCode = userInfo.clazzCode;
          this.form.clazzName = userInfo.clazzName;
          this.form.studentNum = userInfo.studentNum;
          this.form.userType = userInfo.userType;
          // this.getGradeListBySchool(this.form.schoolCode);
          // this.getClazzListByGrade(this.form.gradeCode);
        }
      });
    },
    addUser() {
      if (
        this.form.nickName == "" &&
        this.form.gender == "" &&
        this.form.phone == "" &&
        this.form.email == "" &&
        this.form.schoolCode == "" &&
        this.form.gradeCode == "" &&
        this.form.clazzCode == ""
      ) {
        return uni.showToast({
          title: "信息未完整填写！",
          icon: "none",
        });
      }
      const data = {
        openId: this.openId,
        ...this.form,
      };
      request
        .httpRequest(
          {
            url: "wx/wxRegisterOrLogin",
            method: "post",
          },
          data
        )
        .then((res) => {
          uni.setStorage({
            key: "userInfo",
            data: res.data,
            success: function () {
              uni.showToast({
                title: "保存成功",
                duration: 1500,
              });
              setTimeout(function () {
                uni.reLaunch({
                  url: "/pages/home/<USER>",
                });
              }, 1200);
            },
          });
        });
    },
    // 选择性别
    PickerChange_gender(e) {
      this.index_gender = e.detail.value;
      this.form.gender = this.picker_gender[this.index_gender].id;
    },
    // 选择学校
    selectSchool(e) {
      this.form.schoolName = e.name;
      this.form.schoolCode = e.id;
      this.index_nj = -1;
      this.index_class = -1;
      this.form.gradeCode = "";
      this.form.gradeName = "";
      this.form.clazzCode = "";
      this.form.clazzName = "";
      this.getGradeListBySchool(e.id);
    },
    getGradeListBySchool(schoolCode) {
      request
        .httpRequest(
          {
            url: "sys_org_units/getGradeListBySchool",
            method: "GET",
          },
          {
            schoolCode: schoolCode,
          }
        )
        .then((res) => {
          const data = res.data;
          this.picker_nj = data.map((e) => {
            return {
              id: e.code,
              name: e.name,
            };
          });
          this.index_nj = this.picker_nj.findIndex(
            (e) => e.id == this.form.gradeCode
          );
        });
    },
    getschoolCodeList() {
      let opts = {
        url: "bse_school/getSchoolLists",
        method: "GET",
      };
      request.httpRequest(opts).then((res) => {
        if (res.data) {
          const data = res.data;
          this.schoolCodeList = data.map((e) => {
            return {
              id: e.schoolCode,
              name: e.schoolName,
            };
          });
        }
      });
    },
    PickerChange_nj(e) {
      this.index_nj = e.detail.value;
      this.index_class = -1;
      this.form.clazzCode = "";
      this.form.clazzName = "";
      const data = this.picker_nj[this.index_nj];
      this.form.gradeCode = data.id;
      this.form.gradeName = data.name;
      this.getClazzListByGrade(data.id);
    },
    getClazzListByGrade(gradeCode) {
      request
        .httpRequest(
          {
            url: "sys_org_units/getClazzListByGrade",
            method: "GET",
          },
          {
            gradeCode: gradeCode,
          }
        )
        .then((res) => {
          const data = res.data;
          this.picker_class = data.map((e) => {
            return {
              id: e.code,
              name: e.name,
            };
          });
          this.index_class = this.picker_class.findIndex(
            (e) => e.id == this.form.clazzCode
          );
        });
    },
    PickerChange_class(e) {
      this.index_class = e.detail.value;
      const data = this.picker_class[this.index_class];
      this.form.clazzName = data.name;
      this.form.clazzCode = data.id;
    },
  },
};
</script>

<style lang="scss" scoped>
</style>
