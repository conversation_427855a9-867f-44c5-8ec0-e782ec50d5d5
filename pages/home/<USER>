<!-- 首页 -->
<template>
  <view>
    <view
      class="topBg"
      style="background-image: url(https://cdn.zhoukaiwen.com/cewen_me_bg.png)"
    >
      <view class="cu-custom">
        <view
          class="fixed text-center"
          :style="style"
          style="justify-content: center"
        >
          <view
            class="text-white text-bold text-lg"
            :style="[{ top: StatusBar + 'px' }]"
          >
            作文智判慧改
          </view>
        </view>
      </view>
    </view>

    <view class="mainBox" :style="[{ top: CustomBar + 'px' }]">
      <!-- banner图 -->
      <!-- round-dot square-dot -->
      <view class="padding-bottom-sm padding-top-xs">
        <swiper
          class="screen-swiper round-dot"
          style="min-height: 340rpx"
          :indicator-dots="true"
          :circular="true"
          :autoplay="true"
          interval="5000"
          duration="400"
        >
          <swiper-item
            style="border-radius: 20rpx"
            v-for="(item, index) in bannerList"
            :key="index"
          >
            <image src="/static/banner1.png" mode="widthFix"></image>
          </swiper-item>
        </swiper>
      </view>

      <u-notice-bar
        mode="horizontal"
        bgColor="#ffffff"
        color="#07A069"
        :list="listBar"
      ></u-notice-bar>

      <view class="flex justify-between margin-top align-center" v-if="jobList.length">
        <view class="margin-top-sm margin-bottom-xs">
          <text class="text-bold text-black text-lg">作业列表</text>
        </view>
      </view>

      <view class="job-list" style="padding-top: 0">
        <view
          class="job-item bg-white margin-top-xs"
          v-for="(item, index) in jobList"
          :key="index"
          @click="goProject(item)"
        >
          <view class="title-section flex justify-between">
            <text class="main-title">{{ item.compositionTitle }}</text>
            <text class="text-green padding-right-sm">{{
              CompositionSubmittedStatus[item.answerStatus]
            }}</text>
          </view>
          <view class="tag-group">
            <view class="tag-item">
              <text class="cuIcon-title text-green3"></text>
              <text>{{ item.compositionWenTi }}</text>
            </view>
            <view class="tag-item">
              <text class="cuIcon-write text-green3"></text>
              <text>{{ item.compositionWordCount }}字</text>
            </view>
            <view class="tag-item">
              <text class="cuIcon-rank text-green3"></text>
              <text>满分{{ item.compositionFullScore }}分</text>
            </view>
          </view>

          <view class="padding-lr-sm padding-bottom-sm" style="padding-top: 0">
            <view class="intro-text">{{ item.intro }}</view>
            <view class="preview-images" v-if="item.compositionExamplePath">
              <view class="grid-container">
                <view
                  class="grid-item"
                  v-for="(img, imgIndex) in item.compositionExamplePath
                    .split(',')
                    .slice(0, 3)"
                  :key="imgIndex"
                >
                  <image :src="img" mode="aspectFill" />
                </view>
              </view>
            </view>
            <view class="footer-info text-grey">
              <view class="time-info">
                <text class="cuIcon-time"></text>
                <text>{{ item.compositionBeginTime | timeF }}</text>
                <text class="separator">至</text>
                <text>{{ item.compositionEndTime | timeF }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="flex justify-between margin-top align-center">
        <view class="margin-top-sm margin-bottom-xs">
          <text class="text-bold text-black text-lg">范文列表</text>
          <text class="text-sm margin-left-xs" style="color: #c2c292"
            >数据为AI自动生成</text
          >
        </view>
        <view class="text-smdf text-green3" @click="changeAiType"
          >切换主题</view
        >
      </view>
      <view class="job-list" style="padding-top: 0">
        <view
          class="job-item bg-white margin-top-xs"
          v-for="(item, index) in fwList"
          :key="index"
          @click="goFwdetail(item)"
        >
          <!-- 标题区域 -->
          <view class="title-section flex justify-between">
            <text class="main-title">{{ item.title }}</text>
            <text class="text-green padding-right-sm">{{ item.theme }}</text>
          </view>

          <!-- 标签组 -->
          <view class="tag-group">
            <view class="tag-item">
              <text class="cuIcon-title text-green3"></text>
              <text>{{ item.articleType }}</text>
            </view>
            <view class="tag-item">
              <text class="cuIcon-write text-green3"></text>
              <text>{{ item.wordCount }}字</text>
            </view>
            <view class="tag-item">
              <text class="cuIcon-profile text-green3"></text>
              <text>适合年级：{{ item.grade }}</text>
            </view>
          </view>

          <view class="padding-lr-sm padding-bottom-sm" style="padding-top: 0">
            <!-- 简介内容 -->
            <view class="intro-text">{{ item.content }}</view>

            <!-- 底部信息 -->
            <view class="text-grey flex align-center margin-top">
              <text class="cuIcon-info" style="font-size: 34rpx"></text>
              <text class="margin-left-xs"
                >此篇范文由{{ item.source }}自动生成</text
              >
            </view>
          </view>
        </view>
      </view>
      <!-- 收藏小程序提醒 -->
      <!-- <add-tip :tip="tip" :duration="duration" /> -->

      <view class="page-box" v-if="jobList.length < 1 && fwList.length < 1">
        <view>
          <view class="centre">
            <image
              src="https://cdn.zhoukaiwen.com/noData1.png"
              mode="widthFix"
            ></image>
            <view class="explain">
              暂无数据
              <view class="tips">可以去看看其他板块</view>
            </view>
          </view>
        </view>
      </view>

      <view style="height: 140rpx; width: 1rpx"></view>
    </view>
  </view>
</template>

<script>
import request from "@/common/request.js";
import addTip from "../../components/wxcomponents/struggler-uniapp-add-tip/struggler-uniapp-add-tip.vue";
import moment from "@/components/moment/index.js"; // 格式化时间 插件
import { CompositionSubmittedStatus } from "@/common/constants.js";
export default {
  components: {
    addTip,
  },
  data() {
    return {
      CustomBar: this.CustomBar,
      StatusBar: this.StatusBar,
      CompositionSubmittedStatus,
      searchShow: 1, //1为默认，2为搜索框
      tip: "点击「添加小程序」，下次访问更便捷",
      duration: 1,
      bannerList: [
        {
          imageUrl: "https://cdn.zhoukaiwen.com/zw_banner.jpg",
        },
      ],
      listBar: [
        "《作文智判慧改》最新上线微信小程序🔥 不管是学习还是找灵感，都是不错的选择哦~",
      ],
      searchInput: "",
      jobList: [],
      fwList: [],
    };
  },
  computed: {
    style() {
      const StatusBar = this.StatusBar;
      const CustomBar = this.CustomBar;
      const bgImage = this.bgImage;
      const style = `height:${CustomBar}px;padding-top:${StatusBar}px;`;
      if (this.bgImage) {
        style = `${style}background-image:url(${bgImage});`;
      }
      return style;
    },
  },
  mounted() {
    this.getCompositionJob(this.searchInput);
    this.getFwList("");
  },
  methods: {
    // http://**************:9999/doc.html#/%E5%9F%BA%E7%A1%80%E7%AE%A1%E7%90%86/%E6%96%87%E7%AB%A0%E8%A1%A8/1_2
    getFwList(theme) {
      let opts = {
        url: "bse_article/page",
        method: "GET",
      };

      let params = {
        theme: theme,
      };
      request.httpTokenRequest(opts, params).then((res) => {
        console.log(res.data.records);
        this.fwList = res.data.records;
      });
    },
    searchBtn() {
      this.getCompositionJob(this.searchInput);
    },
    qxBtn() {
      this.searchShow = 1;
      this.searchInput = "";
      this.getCompositionJob(this.searchInput);
    },
    goProject(item) {
      uni.navigateTo({
        url: `/pages/job/job-detail?id=${item.id}&answerStatus=${item.answerStatus}`,
      });
    },
    goFwdetail(item) {
      uni.navigateTo({
        url: `/pages/job/fw-detail?id=${item.id}`,
      });
    },
    async getCompositionJob(searchInput) {
      uni.showLoading({
        title: "加载中",
      });
      try {
        const res = await request.httpTokenRequest(
          {
            url: "biz_composition_release/queryCompositionList",
            method: "post",
          },
          {
            releaseType: "3",
            compositionTitle: searchInput,
          }
        );
        this.jobList = res.data || [];
      } finally {
        uni.hideLoading();
      }
    },
    changeAiType() {
      const itemList = [
        "全部",
        "爱国情怀",
        "节日节气",
        "科技发展",
        "历史文化",
        "自然风光",
      ];
      uni.showActionSheet({
        itemList: itemList,
        success: (res) => {
          if (res.tapIndex == 0) {
            this.getFwList("");
          } else {
            this.getFwList(itemList[res.tapIndex]);
          }
        },
      });
    },
  },
  filters: {
    timeF: function (time) {
      // let time = "1709130283826"
      let mm = Number(time);
      let ss = moment(mm).format("YYYY.MM.DD HH:mm");
      return ss;
    },
  },
};
</script>
<style lang="scss" scoped>
.topBg {
  background-size: 100% 100%;
  /* background-size: cover; */
  height: 450rpx;
  width: 750rpx;
  overflow: hidden;
  flex-direction: column;
  padding: 20rpx;
  position: absolute;
  top: 0;
  left: 0;
}
.mainBox {
  width: 700rpx;
  position: absolute;
  left: 25rpx;
}
.u-type-warning-light-bg {
  border-radius: 15rpx !important;
}
.cu-card.article > .cu-item .content .text-content {
  height: auto !important;
  max-height: 4.8em;
}

.botoomActiveSolid {
  width: 70rpx;
  height: 6rpx;
  margin: 5rpx 0;
  background-color: #0081ff;
}

.searchBtn {
  background-color: #ececec;
  padding: 10rpx 32rpx;
  border-radius: 200rpx;
  color: #999999;
  vertical-align: middle;

  .searchIcon {
    vertical-align: -2rpx;
    font-size: 34rpx;
    margin-right: 10rpx;
  }
}

.searchInputBox {
  width: 600rpx;
  height: 56rpx;
  padding-left: 75rpx;
  background-color: #ffffff;
  position: relative;
  border-radius: 40rpx;
  margin-bottom: 15rpx;

  .searchIcon {
    position: absolute;
    left: 25rpx;
    top: 10rpx;
    font-size: 36rpx;
    vertical-align: -2rpx;
  }

  .searchInput {
    height: 56rpx;
  }
}

.searchInputNo {
  height: 66rpx;
  line-height: 66rpx;
  text-align: center;
}

.centre {
  text-align: center;
  margin: 10rpx auto 50rpx;
  font-size: 32rpx;

  image {
    width: 300rpx;
    border-radius: 50%;
    margin: 0 auto;
  }

  .tips {
    font-size: 24rpx;
    color: #999999;
    margin-top: 20rpx;
  }
}

.job-list {
  padding: 20rpx 0;
  .job-item {
    padding: 20rpx;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    background-color: #fff;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);

    .title-section {
      padding: 10rpx 0;
      position: relative;

      .main-title {
        font-size: 36rpx; // 增大字号
        font-weight: bold;
        color: #2b2b2b; // 加深颜色
        line-height: 1.4;
        padding-left: 24rpx; // 为左边框留出空间
        position: relative;

        // 左边装饰条
        &::before {
          content: "";
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 8rpx;
          height: 32rpx;
          background: #07a069;
          border-radius: 4rpx;
        }
      }
    }

    .tag-group {
      display: flex;
      align-items: center;
      margin: 20rpx 0;
      flex-wrap: nowrap; // 强制单行
      overflow-x: auto; // 超出滚动

      &::-webkit-scrollbar {
        display: none; // 隐藏滚动条
      }

      .tag-item {
        display: flex;
        align-items: center;
        padding: 6rpx 16rpx;
        background: #f8f8f8;
        border-radius: 6rpx;
        margin-right: 16rpx;
        flex-shrink: 0; // 防止压缩

        text {
          &:first-child {
            margin-right: 6rpx;
            font-size: 28rpx;
          }

          &:last-child {
            font-size: 26rpx;
            color: #666;
          }
        }
      }
    }

    .intro-text {
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
      margin: 20rpx 0;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3; // 限制3行
      overflow: hidden;
    }

    .preview-images {
      margin: 20rpx 0;

      .grid-container {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 10rpx;

        .grid-item {
          position: relative;
          width: 100%;
          height: 200rpx; // 固定高度
          border-radius: 6rpx;
          overflow: hidden;

          image {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }

    .footer-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 34rpx;
      font-size: 26rpx;

      .time-info {
        display: flex;
        align-items: center;

        .cuIcon-time {
          font-size: 32rpx;
          margin-right: 12rpx;
        }

        text {
          letter-spacing: 2rpx;

          &:not(.separator) {
            margin: 0 4rpx;
          }

          &.separator {
            margin: 0 16rpx;
          }
        }
      }
    }
  }
}
</style>
