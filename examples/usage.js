/**
 * 新API使用示例
 * 展示如何使用优化后的请求系统
 */

// 导入新的API
import { httpClient, userAPI } from "@/api";

// 示例1: 直接使用httpClient
export const exampleDirectHttp = {
  // GET请求示例
  async getSchoolList() {
    try {
      const result = await httpClient.get("school/list");
      console.log("学校列表:", result.data);
      return result;
    } catch (error) {
      console.error("获取学校列表失败:", error);
    }
  },

  // POST请求示例
  async submitEssay(essayData) {
    try {
      const result = await httpClient.post("essay/submit", essayData, {
        loadingText: "提交中...",
      });
      uni.showToast({ title: "提交成功", icon: "success" });
      return result;
    } catch (error) {
      // 错误已经在httpClient中处理了，这里可以做额外处理
      console.error("提交作文失败:", error);
    }
  },

  // 不需要认证的请求
  async getPublicData() {
    try {
      const result = await httpClient.get(
        "public/data",
        {},
        {
          needAuth: false,
          showLoading: false,
        }
      );
      return result;
    } catch (error) {
      console.error("获取公开数据失败:", error);
    }
  },
};

// 示例2: 使用userAPI（支持缓存）
export const exampleUserAPI = {
  // 应用启动时初始化缓存
  initApp() {
    userAPI.initUserCache(); // 初始化用户信息缓存
  },

  // 登录示例
  async handleLogin(formData) {
    try {
      const result = await userAPI.login({
        userName: formData.userName,
        password: formData.password,
      });

      // 登录成功，跳转到首页
      uni.showToast({ title: "登录成功", icon: "success" });
      setTimeout(() => {
        uni.reLaunch({ url: "/pages/home/<USER>" });
      }, 1500);

      return result;
    } catch (error) {
      // 错误已经在userAPI中处理了
      console.error("登录失败:", error);
    }
  },

  // 获取用户信息示例（优先从缓存）
  async getUserInfo(forceRefresh = false) {
    try {
      const result = await userAPI.getUserInfo(forceRefresh);
      console.log("用户信息:", result.data);
      return result;
    } catch (error) {
      console.error("获取用户信息失败:", error);
    }
  },

  // 更新用户信息示例（自动更新缓存）
  async updateProfile(userInfo) {
    try {
      const result = await userAPI.updateUserInfo(userInfo);
      uni.showToast({ title: "保存成功", icon: "success" });

      // 用户信息已自动更新到缓存，可以直接获取最新信息
      const latestUserInfo = userAPI.getLocalUserInfo();
      console.log("最新用户信息:", latestUserInfo);

      return result;
    } catch (error) {
      console.error("更新用户信息失败:", error);
    }
  },

  // 强制刷新用户信息
  async refreshUserInfo() {
    try {
      const result = await userAPI.refreshUserInfo();
      uni.showToast({ title: "信息已更新", icon: "success" });
      return result;
    } catch (error) {
      console.error("刷新用户信息失败:", error);
    }
  },

  // 检查登录状态示例
  checkLoginStatus() {
    if (!userAPI.isLoggedIn()) {
      uni.showModal({
        title: "提示",
        content: "请先登录",
        success: (res) => {
          if (res.confirm) {
            uni.navigateTo({ url: "/pages/user/login" });
          }
        },
      });
      return false;
    }
    return true;
  },
};

// 示例3: 在Vue页面中使用
export const vuePageExample = {
  data() {
    return {
      userInfo: null,
      schoolList: [],
    };
  },

  async onLoad() {
    // 初始化用户缓存
    userAPI.initUserCache();

    // 检查登录状态
    if (userAPI.isLoggedIn()) {
      // 获取用户信息（优先从缓存）
      this.userInfo = userAPI.getLocalUserInfo();

      // 获取学校列表
      await this.loadSchoolList();
    } else {
      // 跳转到登录页
      uni.reLaunch({ url: "/pages/user/login" });
    }
  },

  methods: {
    async loadSchoolList() {
      try {
        const result = await httpClient.get("school/list");
        this.schoolList = result.data || [];
      } catch (error) {
        console.error("加载学校列表失败:", error);
      }
    },

    async handleLogout() {
      uni.showModal({
        title: "确认",
        content: "确定要退出登录吗？",
        success: (res) => {
          if (res.confirm) {
            userAPI.logout();
          }
        },
      });
    },
  },
};

// 示例4: 微信登录流程
export const wxLoginExample = {
  async handleWxLogin() {
    try {
      // 1. 获取微信授权码
      const loginRes = await new Promise((resolve, reject) => {
        uni.login({
          provider: "weixin",
          success: resolve,
          fail: reject,
        });
      });

      // 2. 获取AccessToken
      const tokenRes = await userAPI.getWxAccessToken(loginRes.code);
      const { openid } = JSON.parse(tokenRes.data);

      // 3. 检查用户是否已注册
      const userInfo = await userAPI.getUserInfo({ openId: openid });

      if (userInfo.data && userInfo.data.id) {
        // 用户已存在，直接登录
        uni.setStorageSync("userInfo", userInfo.data);
        uni.reLaunch({ url: "/pages/home/<USER>" });
      } else {
        // 用户不存在，跳转到注册页面
        uni.setStorageSync("openId", openid);
        uni.navigateTo({ url: "/pages/me/profile" });
      }
    } catch (error) {
      console.error("微信登录失败:", error);
      uni.showToast({ title: "微信登录失败", icon: "none" });
    }
  },
};

// 示例5: 错误处理
export const errorHandlingExample = {
  async handleRequestWithCustomError() {
    try {
      const result = await httpClient.post(
        "some/api",
        {},
        {
          showError: false, // 不显示默认错误提示
        }
      );
      return result;
    } catch (error) {
      // 自定义错误处理
      if (error.code === 401) {
        uni.showModal({
          title: "登录过期",
          content: "您的登录已过期，请重新登录",
          success: (res) => {
            if (res.confirm) {
              userAPI.logout();
            }
          },
        });
      } else {
        uni.showToast({
          title: `操作失败: ${error.message}`,
          icon: "none",
        });
      }
    }
  },
};
